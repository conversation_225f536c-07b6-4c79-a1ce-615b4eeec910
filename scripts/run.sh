#!/bin/bash

PORT=$1

source venv/bin/activate

cp /usr/lib/python3.8/lib-dynload/_bz2.cpython-38-x86_64-linux-gnu.so  /usr/local/lib/python3.11/_bz2.cpython-311-x86_64-linux-gnu.so
cp /usr/lib/python3.8/lib-dynload/_lzma.cpython-38-x86_64-linux-gnu.so  /usr/local/lib/python3.11/_lzma.cpython-311-x86_64-linux-gnu.so

echo "Starting API..."
nohup venv/bin/python -m uvicorn api:app --port ${PORT} --host "0.0.0.0" >> ./log/nohup.log 2>&1 &

echo "Starting background processing..."
nohup venv/bin/python ./main.py 2>&1 | tee -a ./log/nohup.log

echo "All started!"

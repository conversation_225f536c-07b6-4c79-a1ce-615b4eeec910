from unittest.mock import Mock, patch, MagicMock

import numpy as np
import pandas as pd
import pytest

from src.common.consts import EMBEDDING_MODEL_NAME
from src.data_structures.cluster_result import ClusterResult
from src.data_structures.embedding_encoder import BGEM3FlagModelEmbeddingEncoder
from src.data_structures.parsed_address import ParsedAddress
from src.data_structures.request import Request
from src.processors.address_clusterer import AgglomerativeAddressClusterer
from src.processors.embedding_generator import AddressEmbeddingGenerator


@pytest.fixture
def sample_parsed_address():
    return ParsedAddress(
        address="8 Vo<PERSON>, Ph<PERSON>ờng 22, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Việt Nam",
        formatted_address="Số 8, <PERSON><PERSON>, Phường 22, <PERSON><PERSON><PERSON>n B<PERSON>nh Thạnh, Tp<PERSON> <PERSON><PERSON>, Việt Nam",
        freetext_formatted_address="Số 8, <PERSON><PERSON> Du<PERSON>",
        state="TP<PERSON> <PERSON><PERSON> Chí Minh",
        city="Quận <PERSON>h<PERSON>nh",
        district="Phường 22",
        freetext_formatted_corpus_embeddings=None
    )


@pytest.fixture
def sample_parsed_address_2():
    return ParsedAddress(
        address="10 Võ Duy Ninh, Phường 22, Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam",
        formatted_address="Số 10, Võ Duy Ninh, Phường 22, Quận Bình Thạnh, Tp. Hồ Chí Minh, Việt Nam",
        freetext_formatted_address="Số 10, Võ Duy Ninh",
        state="TP. Hồ Chí Minh",
        city="Quận Bình Thạnh",
        district="Phường 22",
        freetext_formatted_corpus_embeddings=None
    )


@pytest.fixture
def sample_parsed_address_3():
    return ParsedAddress(
        address="11 Võ Duy Ninh, Phường 22, Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam",
        formatted_address="Số 11, Võ Duy Ninh, Phường 22, Quận Bình Thạnh, Tp. Hồ Chí Minh, Việt Nam",
        freetext_formatted_address="Số 11, Võ Duy Ninh",
        state="TP. Hồ Chí Minh",
        city="Quận Bình Thạnh",
        district="Phường 22",
        freetext_formatted_corpus_embeddings=None
    )


@pytest.fixture
def mock_address_parser(sample_parsed_address, sample_parsed_address_2, sample_parsed_address_3):
    parser = MagicMock()

    def parse_side_effect(address):
        if "8" in address:
            return sample_parsed_address
        elif "10" in address:
            return sample_parsed_address_2
        elif "11" in address:
            return sample_parsed_address_3
        else:
            return None

    parser.parse.side_effect = parse_side_effect
    return parser


@pytest.fixture
def mock_embedding_generator():
    generator = Mock()
    # Add a default return value since we're using real generator in some tests
    generator.generate_embedding.return_value = np.array([0.1, 0.2, 0.3])
    return generator


@pytest.fixture
def clusterer(mock_address_parser, mock_embedding_generator):
    return AgglomerativeAddressClusterer(mock_address_parser, mock_embedding_generator)


@pytest.fixture
def sample_request():
    timestamp = 1743674030  # Thursday, April 3, 2025 5:53:50 PM GMT+08:00
    return Request(
        request_id="req1",
        event_timestamp=timestamp,
        shopee_user_id=11,
        now_user_id=12,
        total_discount=5000000000,
        restaurant_id=13,
        delivery_address="8 Võ Duy Ninh, Phường 22, Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam"
    )


@pytest.fixture
def sample_request_2():
    timestamp = 1743757900  # Friday, April 4, 2025 5:11:40 PM GMT+08:00
    return Request(
        request_id="req2",
        event_timestamp=timestamp,
        shopee_user_id=21,
        now_user_id=22,
        total_discount=6000000000,
        restaurant_id=13,
        delivery_address="10 Võ Duy Ninh, Phường 22, Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam"
    )


@pytest.fixture
def sample_request_3():
    timestamp = 1743757901  # Friday, April 4, 2025 5:11:41 PM GMT+08:00
    return Request(
        request_id="req3",  # Changed to unique ID to avoid confusion
        event_timestamp=timestamp,
        shopee_user_id=31,
        now_user_id=32,
        total_discount=7000000000,
        restaurant_id=13,
        delivery_address="11 Võ Duy Ninh, Phường 22, Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam"
    )


def test_cluster_new_request_parse_failure(clusterer, sample_request):
    clusterer.address_parser.parse.return_value = None

    result = clusterer.cluster_new_request(sample_request)

    assert isinstance(result, ClusterResult)
    assert result.request_id == "req1"
    assert result.same_cluster_restaurant_user_count == 1


def test_cluster_new_request_already_clustered(clusterer, sample_request, sample_parsed_address):
    clusterer.address_parser.parse.return_value = sample_parsed_address
    clusterer.embedding_generator.generate_embedding.return_value = np.array([0.1, 0.2, 0.3])

    # Setup existing cluster data with correct group_key
    group_key = ("TP. Hồ Chí Minh", "Quận Bình Thạnh", "Phường 22")
    clusterer.address_to_cluster_id[group_key] = pd.DataFrame({
        'address': ["Số 8, Võ Duy Ninh"],
        'cluster_id': [0]
    })
    clusterer.date_to_group_to_address_df["2025-04-03"] = {
        group_key: pd.DataFrame({
            'request_id': ["req1"],
            'now_user_id': [12],
            'restaurant_id': [13],
            'freetext_formatted_address': ["Số 8, Võ Duy Ninh"]
        })
    }

    with patch.object(clusterer, '_build_result') as mock_build:
        mock_build.return_value = ClusterResult("req1", 1)
        result = clusterer.cluster_new_request(sample_request)

        assert mock_build.called
        assert result.request_id == "req1"
        assert result.same_cluster_restaurant_user_count == 1


def test_cluster_new_request_clustering_failure(clusterer, sample_request, sample_parsed_address):
    clusterer.address_parser.parse.return_value = sample_parsed_address
    clusterer.embedding_generator.generate_embedding.return_value = np.array([0.1, 0.2, 0.3])

    with patch.object(clusterer, '_do_clustering') as mock_cluster:
        mock_cluster.return_value = False

        result = clusterer.cluster_new_request(sample_request)

        assert mock_cluster.called
        assert result.request_id == "req1"
        assert result.same_cluster_restaurant_user_count == 1


def test_cluster_new_request_thread_safety(clusterer, sample_request, sample_parsed_address):
    clusterer.address_parser.parse.return_value = sample_parsed_address
    clusterer.embedding_generator.generate_embedding.return_value = np.array([0.1, 0.2, 0.3])

    mock_lock = MagicMock()
    mock_lock.__enter__.return_value = mock_lock
    mock_lock.__exit__.return_value = None

    with patch('threading.RLock', return_value=mock_lock), \
            patch.object(clusterer, '_do_clustering') as mock_cluster, \
            patch.object(clusterer, '_build_result') as mock_build:
        clusterer.lock = mock_lock
        mock_cluster.return_value = True
        mock_build.return_value = ClusterResult("req1", 1)

        clusterer.cluster_new_request(sample_request)

        assert mock_lock.__enter__.called
        assert mock_lock.__exit__.called
        assert mock_lock.__enter__.call_count >= 2
        assert mock_lock.__exit__.call_count >= 2


def test_cluster_new_request_with_existing_cluster(clusterer, sample_request, sample_request_2, sample_request_3):
    embedding_generator = AddressEmbeddingGenerator(BGEM3FlagModelEmbeddingEncoder(EMBEDDING_MODEL_NAME))
    clusterer.embedding_generator = embedding_generator

    result_1 = clusterer.cluster_new_request(sample_request)
    result_2 = clusterer.cluster_new_request(sample_request_2)
    clusterer.remove_date_data("2025-04-03")
    result_3 = clusterer.cluster_new_request(sample_request_3)

    assert result_1.same_cluster_restaurant_user_count == 1
    assert result_2.same_cluster_restaurant_user_count == 2
    assert result_3.same_cluster_restaurant_user_count == 2


if __name__ == "__main__":
    pytest.main()
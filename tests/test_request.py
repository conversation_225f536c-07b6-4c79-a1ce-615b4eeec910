import unittest
from unittest.mock import patch, MagicMock
import sys

# Mock the sccclient module before any imports
sys.modules['sccclient'] = MagicMock()

from src.data_structures.request import Request


class TestRequest(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.base_request_data = {
            'request_id': 'test_req_123',
            'event_timestamp': 1743674030,
            'shopee_user_id': 12345,
            'now_user_id': 67890,
            'total_discount': 1000,
            'restaurant_id': 999,
            'delivery_address': 'Test Address, Test City',
            'order_id': '54321'
        }

    def test_request_initialization_without_dish_category_ids(self):
        """Test Request initialization without dish_category_ids parameter."""
        request = Request(**self.base_request_data)
        
        self.assertEqual(request.request_id, 'test_req_123')
        self.assertEqual(request.event_timestamp, 1743674030)
        self.assertEqual(request.shopee_user_id, 12345)
        self.assertEqual(request.now_user_id, 67890)
        self.assertEqual(request.total_discount, 1000)
        self.assertEqual(request.restaurant_id, 999)
        self.assertEqual(request.delivery_address, 'Test Address, Test City')
        self.assertEqual(request.order_id, 54321)
        self.assertEqual(request.dish_category_ids, [])  # Should default to empty list

    def test_request_initialization_with_dish_category_ids(self):
        """Test Request initialization with dish_category_ids parameter."""
        dish_ids = [1, 2, 3, 4, 5]
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)
        
        self.assertEqual(request.dish_category_ids, dish_ids)

    def test_request_initialization_with_none_dish_category_ids(self):
        """Test Request initialization with None dish_category_ids parameter."""
        request_data = {**self.base_request_data, 'dish_category_ids': None}
        request = Request(**request_data)
        
        self.assertEqual(request.dish_category_ids, [])  # Should default to empty list

    def test_to_dict_includes_dish_category_ids(self):
        """Test that to_dict method includes dish_category_ids."""
        dish_ids = [10, 20, 30]
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)
        
        result_dict = request.to_dict()
        
        self.assertIn('dish_category_ids', result_dict)
        self.assertEqual(result_dict['dish_category_ids'], dish_ids)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_empty_dish_category_ids(self, mock_config_manager):
        """Test is_dishes_valid returns True for empty dish_category_ids."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [1, 2, 3, 4, 5]
        mock_config_manager.return_value = mock_config_instance

        request = Request(**self.base_request_data)  # No dish_category_ids

        result = request.is_dishes_valid()

        self.assertTrue(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_all_valid_dish_category_ids(self, mock_config_manager):
        """Test is_dishes_valid returns True when all dish_category_ids are valid."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [1, 3, 5, 7]  # All valid
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()

        self.assertTrue(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_some_invalid_dish_category_ids(self, mock_config_manager):
        """Test is_dishes_valid returns False when some dish_category_ids are invalid."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [1, 2, 3, 4, 5]
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [1, 3, 6, 8]  # 6 and 8 are invalid
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()
        
        self.assertFalse(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_all_invalid_dish_category_ids(self, mock_config_manager):
        """Test is_dishes_valid returns False when all dish_category_ids are invalid."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [1, 2, 3, 4, 5]
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [10, 11, 12]  # All invalid
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()

        self.assertFalse(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_empty_config_dish_category_ids(self, mock_config_manager):
        """Test is_dishes_valid returns False when config has empty dish_category_ids."""
        # Setup mock config manager with empty valid IDs
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = []
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [1, 2, 3]  # Any IDs will be invalid
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()

        self.assertFalse(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_single_valid_dish_category_id(self, mock_config_manager):
        """Test is_dishes_valid returns True for single valid dish_category_id."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [100, 200, 300]
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [200]  # Single valid ID
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()

        self.assertTrue(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_single_invalid_dish_category_id(self, mock_config_manager):
        """Test is_dishes_valid returns False for single invalid dish_category_id."""
        # Setup mock config manager
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = [100, 200, 300]
        mock_config_manager.return_value = mock_config_instance

        dish_ids = [400]  # Single invalid ID
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()
        
        self.assertFalse(result)

    @patch('src.data_structures.request.ConfigManager.get_instance')
    def test_isDishesValid_performance_with_large_lists(self, mock_config_manager):
        """Test is_dishes_valid performance with large lists (uses set for O(1) lookup)."""
        # Setup mock config manager with large list
        large_valid_ids = list(range(1, 10001))  # 1 to 10000
        mock_config_instance = MagicMock()
        mock_config_instance.dish_category_ids = large_valid_ids
        mock_config_manager.return_value = mock_config_instance

        # Test with valid IDs from the large list
        dish_ids = [1, 100, 1000, 5000, 9999]
        request_data = {**self.base_request_data, 'dish_category_ids': dish_ids}
        request = Request(**request_data)

        result = request.is_dishes_valid()

        self.assertTrue(result)

        # Test with some invalid IDs
        dish_ids_with_invalid = [1, 100, 10001, 10002]  # 10001, 10002 are invalid
        request_data_invalid = {**self.base_request_data, 'dish_category_ids': dish_ids_with_invalid}
        request_invalid = Request(**request_data_invalid)

        result_invalid = request_invalid.is_dishes_valid()
        
        self.assertFalse(result_invalid)


if __name__ == '__main__':
    unittest.main()

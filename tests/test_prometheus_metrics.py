import unittest
from unittest.mock import patch, MagicMock

from prometheus_client import REGISTRY

from src.utils.prometheus import (
    track_time,
    track_cluster_size,
    track_cancel_order_status,
    track_threshold_status,
    track_api_error,
    cancel_order_requests_total,
    cancel_order_by_threshold_total,
    cancel_order_api_errors_total,
    cancel_order_request_duration_seconds,
    cancel_order_cluster_size
)


class TestPrometheusMetrics(unittest.TestCase):
    def setUp(self):
        # Reset metrics before each test
        for metric in [
            cancel_order_requests_total,
            cancel_order_by_threshold_total,
            cancel_order_api_errors_total,
            cancel_order_cluster_size
        ]:
            if hasattr(metric, '_metrics'):
                metric._metrics.clear()

    def test_track_cancel_order_status(self):
        # Test tracking different statuses with API codes and reasons
        track_cancel_order_status("success", api_code="0", reason="success")
        track_cancel_order_status("failed", api_code="1", reason="api_error_invalid_order")
        track_cancel_order_status("skipped", reason="below_threshold")

        # Get the current value for each label combination
        success_count = REGISTRY.get_sample_value('cancel_order_requests_total',
                                               {'status': 'success', 'api_code': '0', 'reason': 'success'})
        failed_count = REGISTRY.get_sample_value('cancel_order_requests_total',
                                              {'status': 'failed', 'api_code': '1', 'reason': 'api_error_invalid_order'})
        skipped_count = REGISTRY.get_sample_value('cancel_order_requests_total',
                                               {'status': 'skipped', 'api_code': '', 'reason': 'below_threshold'})

        # Verify counts
        self.assertEqual(success_count, 1.0)
        self.assertEqual(failed_count, 1.0)
        self.assertEqual(skipped_count, 1.0)

    def test_track_threshold_status(self):
        # Test tracking different threshold statuses
        track_threshold_status(True)  # below threshold
        track_threshold_status(False)  # hit threshold

        # Get the current value for each label
        below_threshold_count = REGISTRY.get_sample_value('cancel_order_by_threshold_total', {'threshold_status': 'below_threshold'})
        hit_threshold_count = REGISTRY.get_sample_value('cancel_order_by_threshold_total', {'threshold_status': 'hit_threshold'})

        # Verify counts
        self.assertEqual(below_threshold_count, 1.0)
        self.assertEqual(hit_threshold_count, 1.0)

    def test_track_api_error(self):
        # Test tracking different API error types
        track_api_error("http_error")
        track_api_error("api_error")
        track_api_error("exception")

        # Get the current value for each label
        http_error_count = REGISTRY.get_sample_value('cancel_order_api_errors_total', {'error_type': 'http_error'})
        api_error_count = REGISTRY.get_sample_value('cancel_order_api_errors_total', {'error_type': 'api_error'})
        exception_count = REGISTRY.get_sample_value('cancel_order_api_errors_total', {'error_type': 'exception'})

        # Verify counts
        self.assertEqual(http_error_count, 1.0)
        self.assertEqual(api_error_count, 1.0)
        self.assertEqual(exception_count, 1.0)

    def test_track_cluster_size(self):
        # Test tracking cluster size
        track_cluster_size(5)

        # Get the current value
        cluster_size = REGISTRY.get_sample_value('cancel_order_cluster_size')

        # Verify value
        self.assertEqual(cluster_size, 5.0)

        # Update with a new value
        track_cluster_size(10)

        # Get the updated value
        updated_cluster_size = REGISTRY.get_sample_value('cancel_order_cluster_size')

        # Verify the value was updated
        self.assertEqual(updated_cluster_size, 10.0)



    def test_track_time_decorator(self):
        # Create a test function with the decorator
        @track_time(cancel_order_request_duration_seconds)
        def test_function():
            pass

        # Call the function
        test_function()

        # Verify the histogram count was incremented
        count = REGISTRY.get_sample_value('cancel_order_request_duration_seconds_count')

        # Verify count
        self.assertEqual(count, 1.0)


if __name__ == '__main__':
    unittest.main()

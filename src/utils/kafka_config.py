from src.common.consts import REQUEST_KAFKA_SASL_MECHANISM, LOGGER_KAFKA_SASL_MECHANISM, REQUEST_KAFKA_SECURITY_PROTOCOL


def build_kafka_consumer_config(bootstrap_servers: str, group_id: str, username: str = "", password: str = "",
                                security_protocol: str = None, sasl_mechanism: str = None,
                                auto_commit: bool = True, group_instance_id: str = "",
                                auto_offset_reset: str = "earliest") -> dict:
    if sasl_mechanism is None:
        sasl_mechanism = REQUEST_KAFKA_SASL_MECHANISM or "PLAIN"

    if security_protocol is None:
        security_protocol = REQUEST_KAFKA_SECURITY_PROTOCOL or "SASL_PLAINTEXT"

    conf = {
        'bootstrap.servers': bootstrap_servers,
        'group.id': group_id,
        'auto.offset.reset': auto_offset_reset,
        'socket.keepalive.enable': True,
        'enable.auto.commit': auto_commit,
        'max.poll.interval.ms': 15 * 60 * 1000,  # 15 minutes.
        'session.timeout.ms': 5 * 60 * 1000,  # 5 minutes.
        'heartbeat.interval.ms': 3 * 60 * 1000,  # 1 minute.
        'fetch.max.bytes': 30 * 1024 * 1024,  # 30 MB,
        'fetch.message.max.bytes': 30 * 1024 * 1024  # 30 MB
    }

    if username and password:
        conf['security.protocol'] = security_protocol
        conf['sasl.mechanism'] = sasl_mechanism
        conf['sasl.username'] = username
        conf['sasl.password'] = password

    if group_instance_id != "":
        conf['group.instance.id'] = group_instance_id

    print(f'Config: {conf}')

    return conf


def build_kafka_producer_config(bootstrap_servers: str, username: str = "", password: str = "",
                                security_protocol: str = "SASL_PLAINTEXT", sasl_mechanism: str = None) -> dict:
    if sasl_mechanism is None:
        sasl_mechanism = LOGGER_KAFKA_SASL_MECHANISM or "SCRAM-SHA-512"

    conf = {
        'bootstrap.servers': bootstrap_servers,
        'message.max.bytes': 10 * 1024 * 1024,  # 10 MB
    }

    if username and password:
        conf['security.protocol'] = security_protocol
        conf['sasl.mechanism'] = sasl_mechanism
        conf['sasl.username'] = username
        conf['sasl.password'] = password

    return conf

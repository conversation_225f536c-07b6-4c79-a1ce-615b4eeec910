import time
from functools import wraps
from typing import Callable, Any

from prometheus_client import Counter, Histogram, Gauge, Summary

# Initialize metrics
# Counters
cancel_order_requests_total = Counter(
    'cancel_order_requests_total',
    'Total number of cancel order requests',
    ['status', 'api_code', 'reason']
    # status: success, failed, skipped; api_code: API response code; reason: reason for status
)

cancel_order_by_threshold_total = Counter(
    'cancel_order_by_threshold_total',
    'Total number of cancel order requests by cluster threshold',
    ['threshold_status']  # below_threshold, hit_threshold
)

cancel_order_api_errors_total = Counter(
    'cancel_order_api_errors_total',
    'Total number of API errors when canceling orders',
    ['error_type']  # http_error, api_error, exception
)

# Address Parser Metrics
address_division_parsing_total = Counter(
    'address_division_parsing_total',
    'Total number of parsed addresses by division info status',
    ['division_status']  # empty_all_divisions, has_division_info
)

# Address Clusterer Metrics
# Counters
clustering_operations_total = Counter(
    'clustering_operations_total',
    'Total number of clustering operations',
    ['status', 'group_key']  # status: success, failed; group_key: state_city_district
)

address_processing_total = Counter(
    'address_processing_total',
    'Total number of addresses processed',
    ['operation_type']  # operation_type: added, clustered, parsed, etc.
)

# Histograms
cancel_order_request_duration_seconds = Histogram(
    'cancel_order_request_duration_seconds',
    'Duration of cancel order requests in seconds',
    buckets=[0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 10.0]
)

clustering_duration_seconds = Histogram(
    'clustering_duration_seconds',
    'Duration of clustering operations in seconds',
    ['operation_type'],  # operation_type: do_clustering, cluster_new_request, cluster_in_batch
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 10.0, 30.0, 60.0]
)

address_parsing_duration_seconds = Histogram(
    'address_parsing_duration_seconds',
    'Duration of address parsing operations in seconds',
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
)

serialization_duration_seconds = Histogram(
    'serialization_duration_seconds',
    'Duration of serialization/deserialization operations in seconds',
    ['operation_type'],  # operation_type: serialize, deserialize, serialize_by_date
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0]
)

# Gauges
cancel_order_cluster_size = Gauge(
    'cancel_order_cluster_size',
    'Size of the cluster for the most recent cancel order request'
)

address_group_size = Gauge(
    'address_group_size',
    'Number of addresses in each address group',
    ['group_key']  # group_key: state_city_district
)

active_address_groups = Gauge(
    'active_address_groups',
    'Number of active address groups being tracked'
)

active_dates_tracked = Gauge(
    'active_dates_tracked',
    'Number of dates with address data being tracked'
)


def track_time(metric: Histogram) -> Callable:
    """
    Decorator to track the execution time of a function using a Prometheus Histogram.

    Args:
        metric: The Prometheus Histogram to record the execution time

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            metric.observe(execution_time)
            return result

        return wrapper

    return decorator


def track_cluster_size(size: int, metric: Gauge = cancel_order_cluster_size) -> None:
    """
    Track the size of a cluster.

    Args:
        size: The size of the cluster
        metric: The Prometheus Gauge to record the size
    """
    metric.set(size)


def track_cancel_order_status(status: str, api_code: str = "", reason: str = "",
                              metric: Counter = cancel_order_requests_total) -> None:
    """
    Track the status of a cancel order request with additional response details.

    Args:
        status: The status of the request (success, failed, skipped)
        api_code: The response code from the API (e.g., "0" for success, "1" for business error)
        reason: The reason for the status (e.g., "below_threshold", "missing_order_id", "success")
        metric: The Prometheus Counter to increment
    """
    # Ensure api_code and reason are strings
    api_code_str = str(api_code) if api_code is not None else ""
    reason_str = str(reason) if reason is not None else ""

    # Truncate reason if it's too long (Prometheus labels shouldn't be too large)
    if len(reason_str) > 50:
        reason_str = reason_str[:47] + "..."

    metric.labels(status=status, api_code=api_code_str, reason=reason_str).inc()


def track_threshold_status(below_threshold: bool, metric: Counter = cancel_order_by_threshold_total) -> None:
    """
    Track the threshold status of a cancel order request.

    Args:
        below_threshold: True if below threshold, False if equal or above threshold
        metric: The Prometheus Counter to increment
    """
    status = "below_threshold" if below_threshold else "hit_threshold"
    metric.labels(threshold_status=status).inc()


def track_api_error(error_type: str, metric: Counter = cancel_order_api_errors_total) -> None:
    """
    Track API errors when canceling orders.

    Args:
        error_type: The type of error (http_error, api_error, exception)
        metric: The Prometheus Counter to increment
    """
    metric.labels(error_type=error_type).inc()


def track_clustering_operation(status: str, group_key: tuple, metric: Counter = clustering_operations_total) -> None:
    """
    Track clustering operations with status and group key.

    Args:
        status: The status of the operation (success, failed)
        group_key: The group key tuple (state, city, district)
        metric: The Prometheus Counter to increment
    """
    # Convert tuple to string representation for the label
    group_key_str = "_".join(str(part) for part in group_key)
    metric.labels(status=status, group_key=group_key_str).inc()


def track_address_processing(operation_type: str, metric: Counter = address_processing_total) -> None:
    """
    Track address processing operations.

    Args:
        operation_type: The type of operation (added, clustered, parsed, etc.)
        metric: The Prometheus Counter to increment
    """
    metric.labels(operation_type=operation_type).inc()


def track_address_group_size(group_key: tuple, size: int, metric: Gauge = address_group_size) -> None:
    """
    Track the size of an address group.

    Args:
        group_key: The group key tuple (state, city, district)
        size: The size of the address group
        metric: The Prometheus Gauge to set
    """
    # Convert tuple to string representation for the label
    group_key_str = "_".join(str(part) for part in group_key)
    metric.labels(group_key=group_key_str).set(size)


def track_active_groups_count(count: int, metric: Gauge = active_address_groups) -> None:
    """
    Track the number of active address groups.

    Args:
        count: The number of active address groups
        metric: The Prometheus Gauge to set
    """
    metric.set(count)


def track_active_dates_count(count: int, metric: Gauge = active_dates_tracked) -> None:
    """
    Track the number of dates with address data.

    Args:
        count: The number of dates with address data
        metric: The Prometheus Gauge to set
    """
    metric.set(count)


def track_address_division_parsing(has_division_info: bool, 
                                   metric: Counter = address_division_parsing_total) -> None:
    """
    Track the division info status of parsed addresses.

    Args:
        has_division_info: True if address has at least one division field (state, city, district), 
                          False if all division fields are empty
        metric: The Prometheus Counter to increment
    """
    status = "has_division_info" if has_division_info else "empty_all_divisions"
    metric.labels(division_status=status).inc()

import logging
from logging.handlers import RotatingFileHandler
from src.common.consts import LOG_PATH, ENV_PROD, ENV


class LevelFilter(logging.Filter):
    def __init__(self, level):
        super().__init__()
        self.level = level

    def filter(self, record):
        return record.levelno == self.level


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

if ENV != ENV_PROD:
    # Add a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(formatter)

    logger.addHandler(console_handler)

max_bytes = 100 * 1024 * 1024  # 100 MB
backup_count = 10

debug_handler = RotatingFileHandler(f'{LOG_PATH}/debug.log', maxBytes=max_bytes, backupCount=backup_count)
info_handler = RotatingFileHandler(f'{LOG_PATH}/info.log', maxBytes=max_bytes, backupCount=backup_count)
warning_handler = RotatingFileHandler(f'{LOG_PATH}/warning.log', maxBytes=max_bytes, backupCount=backup_count)
error_handler = RotatingFileHandler(f'{LOG_PATH}/error.log', maxBytes=max_bytes, backupCount=backup_count)

debug_handler.setLevel(logging.DEBUG)
info_handler.setLevel(logging.INFO)
warning_handler.setLevel(logging.WARNING)
error_handler.setLevel(logging.ERROR)

debug_handler.addFilter(LevelFilter(logging.DEBUG))
info_handler.addFilter(LevelFilter(logging.INFO))
warning_handler.addFilter(LevelFilter(logging.WARNING))
error_handler.addFilter(LevelFilter(logging.ERROR))

debug_handler.setFormatter(formatter)
info_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)

logger.addHandler(debug_handler)
logger.addHandler(info_handler)
logger.addHandler(warning_handler)
logger.addHandler(error_handler)

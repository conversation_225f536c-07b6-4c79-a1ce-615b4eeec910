def cast_bool(bool_str: any) -> bool:
    if bool_str is None:
        return False

    if type(bool_str) is bool:
        return bool_str

    return bool_str.lower() in ['true', '1', 't', 'y', 'yes']


def cast_int(value: str, default_value: int | None = None) -> int | None:
    try:
        return int(value)
    except ValueError:
        return default_value


def cast_float(value: str, default_value: float | None = None) -> float | None:
    try:
        return float(value)
    except ValueError:
        return default_value


def cast_long_array(value, default_value=None):
    """Cast a value to a list of long integers.

    Args:
        value: Can be a list, string representation of a list, or None
        default_value: Default value to return if casting fails

    Returns:
        List of integers or default_value if casting fails
    """
    if value is None:
        return default_value or []

    if isinstance(value, list):
        try:
            return [int(x) for x in value]
        except (ValueError, TypeError):
            return default_value or []

    if isinstance(value, str):
        try:
            # Try to parse as JSON array
            import json
            parsed = json.loads(value)
            if isinstance(parsed, list):
                return [int(x) for x in parsed]
            else:
                return default_value or []
        except (json.JSONDecodeError, ValueError, TypeError):
            return default_value or []

    return default_value or []
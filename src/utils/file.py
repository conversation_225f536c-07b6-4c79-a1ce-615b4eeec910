import os
import zipfile
import tempfile
import shutil
from typing import Optional


def write_to_file(file_path: str, content: str, overwrite: bool = True) -> None:
    """Write content to a file, creating directories if needed."""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    open_mode = 'w' if overwrite else 'a'
    with open(file_path, open_mode) as file:
        file.write(content)


def create_zip_archive(source_dir: str, archive_name: str, temp_dir: Optional[str] = None) -> str:
    """
    Create a zip archive of the source directory.
    
    Args:
        source_dir (str): Directory to zip
        archive_name (str): Name for the archive (without .zip extension)
        temp_dir (Optional[str]): Directory to create the zip in. Uses system temp if None.
        
    Returns:
        str: Path to the created zip file
    """
    temp_dir = temp_dir or tempfile.gettempdir()
    zip_path = os.path.join(temp_dir, f"{archive_name}.zip")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, source_dir)
                zipf.write(file_path, arcname)
    
    return zip_path


def extract_zip_archive(zip_path: str, extract_dir: str) -> None:
    """
    Extract a zip archive to the specified directory.
    
    Args:
        zip_path (str): Path to the zip file
        extract_dir (str): Directory to extract to
    """
    os.makedirs(extract_dir, exist_ok=True)
    with zipfile.ZipFile(zip_path, 'r') as zipf:
        zipf.extractall(extract_dir)


def safe_delete_file(file_path: str) -> None:
    """
    Safely delete a file if it exists.
    
    Args:
        file_path (str): Path to the file to delete
    """
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
    except Exception:
        pass  # Ignore errors in cleanup


def ensure_dir_exists(dir_path: str) -> None:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        dir_path (str): Path to the directory
    """
    os.makedirs(dir_path, exist_ok=True)


def clean_directory(dir_path: str) -> None:
    """
    Remove all contents of a directory if it exists.
    
    Args:
        dir_path (str): Path to the directory to clean
    """
    if os.path.exists(dir_path):
        shutil.rmtree(dir_path)
    os.makedirs(dir_path, exist_ok=True)

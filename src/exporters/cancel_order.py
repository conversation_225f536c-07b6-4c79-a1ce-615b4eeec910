import hashlib
import hmac
import json
import time
from typing import Tuple

import requests
from tenacity import stop_after_attempt, wait_exponential, retry

from src.common.consts import (
    CANCEL_API_SCHEME,
    CANCEL_API_PATH,
    CANCEL_API_TIMEOUT_MS,
)
from src.data_structures.cluster_result import ClusterResult
from src.data_structures.request import Request
from src.exporters.base import Exporter
from src.utils.config_manager import ConfigManager
from src.utils.logger import logger
from src.utils.prometheus import (
    track_time,
    track_cluster_size,
    track_cancel_order_status,
    track_threshold_status,
    track_api_error,
    cancel_order_request_duration_seconds
)


class CancelOrderExporter(Exporter):
    def __init__(self, api_scheme: str = None, api_host: str = None, api_path: str = None,
                 strategy_code: str = None, timeout_ms: int = None, api_id: str = None, api_secret: str = None):
        """
        Initialize the CancelOrderExporter.

        Args:
            api_scheme: The HTTP scheme (http/https) for the API request. Defaults to CANCEL_API_SCHEME.
            api_host: The host for the API request.
            api_path: The path for the API request. Defaults to CANCEL_API_PATH.
            strategy_code: The strategy code to use when canceling orders.
            timeout_ms: The timeout in milliseconds for the HTTP request. Defaults to CANCEL_API_TIMEOUT_MS.
            api_id: The app ID for the API request.
            api_secret: The secret key for generating the HMAC signature.
        """
        self.api_scheme = api_scheme or CANCEL_API_SCHEME
        self.api_host = api_host
        self.api_path = api_path or CANCEL_API_PATH
        self.strategy_code = strategy_code
        self.timeout_ms = timeout_ms or CANCEL_API_TIMEOUT_MS
        self.api_id = api_id
        self.api_secret = api_secret

        # Construct the full API endpoint URL
        self.api_endpoint = f"{self.api_scheme}://{self.api_host}{self.api_path}" if all(
            [self.api_scheme, self.api_host, self.api_path]) else None

        # Convert timeout from milliseconds to seconds for requests library
        self.timeout_sec = self.timeout_ms / 1000.0

        if not self.api_scheme:
            logger.warning("CancelOrderExporter: No API scheme provided")
        if not self.api_host:
            logger.warning("CancelOrderExporter: No API host provided")
        if not self.api_path:
            logger.warning("CancelOrderExporter: No API path provided")
        if not self.strategy_code:
            logger.warning("CancelOrderExporter: No strategy code provided")
        if not self.api_id:
            logger.warning("CancelOrderExporter: No API ID provided")
        if not self.api_secret:
            logger.warning("CancelOrderExporter: No API secret provided")

    def _generate_hmac_signature(self, method: str, path: str, body: str, timestamp: int) -> str:
        """
        Generate HMAC signature for the API request.

        Args:
            method: HTTP method (e.g., 'POST')
            path: API path (e.g., '/api/v5/anti-fraud/notify_illegal_order')
            body: Request body as a JSON string
            timestamp: Current timestamp in seconds

        Returns:
            HMAC signature as a hexadecimal string
        """
        try:
            # Create the base string for HMAC calculation
            base_string = f"{method}|{path}|{body}|{timestamp}"

            # Convert the secret key to bytes
            key = self.api_secret.encode('utf-8')

            # Calculate the HMAC signature
            signature = hmac.new(
                key=key,
                msg=base_string.encode('utf-8'),
                digestmod=hashlib.sha256
            ).hexdigest()

            return signature
        except Exception as e:
            logger.error(f"CancelOrderExporter: Error generating HMAC signature: {e}")
            return ""

    def export(self, data: Tuple[Request, ClusterResult]) -> None:
        """
        Export data by sending a cancel order request if the cluster meets the threshold.

        Args:
            data: A tuple containing the Request and ClusterResult objects
        """
        try:
            request, result = data

            # Track cluster size for metrics
            track_cluster_size(result.same_cluster_restaurant_user_count)

            # Skip if order_id is not available
            if not request.order_id:
                message = f"No order_id available for request {request.request_id}"
                logger.warning(f"CancelOrderExporter: {message}")
                track_cancel_order_status("skipped", reason="missing_order_id")
                return

            # Check if the cluster meets the threshold for cancellation
            threshold = ConfigManager.get_instance().cancel_order_min_cluster_id_count_threshold
            if result.same_cluster_restaurant_user_count < threshold:
                message = f"Cluster count {result.same_cluster_restaurant_user_count} below threshold {threshold}"
                logger.info(f"CancelOrderExporter: {message}, not canceling order {request.order_id}")
                track_cancel_order_status("skipped", reason="below_threshold")
                track_threshold_status(True)  # below threshold
                return
            else:
                # Equal to or above threshold
                track_threshold_status(False)  # hit threshold

            if not ConfigManager.get_instance().exporter_cancel_api_is_on:
                logger.info(f"CancelOrderExporter: Cancel API is not enabled, request: {data[0]}, result: {data[1]}")
                return

            # Skip if required configuration is missing
            if not all(
                    [self.api_scheme, self.api_host, self.api_path, self.strategy_code, self.api_id, self.api_secret]):
                message = "Missing required configuration"
                logger.error(f"CancelOrderExporter: {message}")
                track_cancel_order_status("skipped", reason="missing_config")
                return

            # Prepare request payload
            payload = {
                "order_id": request.order_id,
                "strategy_code": self.strategy_code
            }

            # Convert payload to JSON string
            payload_json = json.dumps(payload)

            # Generate current timestamp
            timestamp = int(time.time())

            # Generate HMAC signature
            signature = self._generate_hmac_signature('POST', self.api_path, payload_json, timestamp)

            # Prepare headers
            headers = {
                'X-FOODY-AUTHORIZATION': signature,
                'X-Foody-App-Id': self.api_id,
                'Host': self.api_host,
                'Timestamp': str(timestamp),
                'Content-Type': 'application/json',
                'X-Request-Id': request.request_id
            }

            logger.info(f"CancelOrderExporter: Sending cancel request for order {request.order_id} "
                        f"with strategy code {self.strategy_code}")

            response = self._request_cancel_api(headers, payload)

            # Process response
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"CancelOrderExporter: Cancel order response: {response_data}")

                # code 0: success, 1: business error, 2: system error, 3: request exception, 4: error params
                response_code = response_data.get("code")
                if response_code == 0:
                    message = f"Successfully canceled order {request.order_id}"
                    logger.info(f"CancelOrderExporter: {message}")
                    track_cancel_order_status("success", api_code=str(response_code), reason="success")
                else:
                    error_code = response_data.get("data", {}).get("error_code", "unknown")
                    error_msg = response_data.get("msg", "No error message")
                    message = f"Failed to cancel order {request.order_id}, code: {response_code}, error_code: {error_code}"
                    logger.warning(f"CancelOrderExporter: {message}, message: {error_msg}")
                    track_cancel_order_status("failed", api_code=str(response_code), reason=f"api_error_{error_code}")
                    track_api_error("api_error")
            else:
                message = f"HTTP error {response.status_code} when canceling order {request.order_id}"
                logger.error(f"CancelOrderExporter: {message}, response: {response.text}")
                track_cancel_order_status("failed", api_code=str(response.status_code), reason="http_error")
                track_api_error("http_error")

        except Exception as e:
            message = f"Error during cancel order request: {e}"
            logger.error(f"CancelOrderExporter: {message}")
            track_cancel_order_status("failed", reason="exception")
            track_api_error("exception")

    @track_time(cancel_order_request_duration_seconds)
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=60))
    def _request_cancel_api(self, headers, payload):
        response = requests.post(
            self.api_endpoint,
            headers=headers,
            json=payload,
            timeout=self.timeout_sec
        )
        return response

import orjson
import time

from confluent_kafka import Producer

from src.data_structures.request import Request
from src.data_structures.cluster_result import ClusterResult
from src.exporters.base import Exporter
from src.utils.kafka_config import build_kafka_producer_config
from src.utils.logger import logger


class LoggerExporter(Exporter):
    def __init__(self, bootstrap_servers: str, topic: str, username: str, password: str):
        self.producer = Producer(build_kafka_producer_config(bootstrap_servers, username, password))
        self.topic = topic

    def export(self, data: tuple[Request, ClusterResult]) -> None:
        try:
            request, result = data

            log_entry = {
                'request': request.to_dict(),
                'result': result.to_dict(),
                'sent_at': int(time.time()),
            }
            self.producer.produce(self.topic, key=str(
                request.request_id), value=orjson.dumps(log_entry))
            self.producer.flush()
        except Exception as e:
            logger.error(f"LoggerExporter: error during produce message: {e}")

import os
import tempfile
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor, as_completed
from datetime import datetime, timedelta
from threading import Lock
from typing import Tuple

import pandas as pd

from src.common.consts import HISTORICAL_DATA_PATH, NUMBER_OF_DAYS_IN_HISTORY, HISTORICAL_CLUSTER_DATA_BASE_PATH, \
    HISTORICAL_CLUSTER_DATA_FOLDER, S3_UMS_DATA_PREFIX
from src.data_loaders.parquet_data_loader import ParquetDataLoader
from src.facades.s3_facade import s3_facade, ums_s3
from src.processors.address_clusterer import AddressClusterer
from src.processors.address_parser import AddressParser
from src.processors.embedding_generator import EmbeddingGenerator
from src.utils.config_manager import ConfigManager
from src.utils.file import create_zip_archive, extract_zip_archive, safe_delete_file, ensure_dir_exists, clean_directory
from src.utils.logger import logger


class StartupProcessor:
    def __init__(self, address_parser: AddressParser, embedding_generator: EmbeddingGenerator,
                 address_clusterer: AddressClusterer):
        self.address_parser = address_parser
        self.embedding_generator = embedding_generator
        self.address_clusterer = address_clusterer
        self.lock = Lock()

    def _get_date_range(self, days: int = NUMBER_OF_DAYS_IN_HISTORY) -> list[str]:
        """Get list of dates in YYYY-MM-DD format for the last n days including today."""
        today = datetime.now()
        dates = [(today - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(days)]
        return dates

    def _load_data_for_date(self, date: str) -> pd.DataFrame:
        """
        Load data from parquet files for a specific date.
        Downloads data from S3 if it doesn't exist locally or if it's today's data.
        
        Args:
            date (str): Date in YYYY-MM-DD format
            
        Returns:
            pd.DataFrame: DataFrame containing the loaded data
        """
        date_path = os.path.join(HISTORICAL_DATA_PATH, date)
        today = datetime.now().strftime('%Y-%m-%d')
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        should_download = date == today or date == yesterday or not os.path.exists(date_path)

        if should_download:
            try:
                # Cleanup the date directory
                if os.path.exists(date_path):
                    clean_directory(date_path)

                # Convert date string to datetime for S3 facade
                date_dt = datetime.strptime(date, '%Y-%m-%d')
                # Download files from S3
                downloaded_files = s3_facade.download_order_data_by_date(
                    date=date_dt,
                    output_dir=HISTORICAL_DATA_PATH,
                    overwrite=(date == today)  # Overwrite if it's today's data
                )
                if not downloaded_files:
                    logger.warning(f"No files found in S3 for date: {date}")
                    return pd.DataFrame()
                logger.info(f"Downloaded {len(downloaded_files)} files for date {date}")
            except Exception as e:
                logger.error(f"Error downloading data from S3 for date {date}: {e}")
                return pd.DataFrame()

        # Load data from local files
        if not os.path.exists(date_path):
            logger.warning(f"No data directory found for date: {date}")
            return pd.DataFrame()

        data_loader = ParquetDataLoader(date_path)
        try:
            df = data_loader.load_data()
            logger.info(f"Loaded {len(df)} records for date {date}")
            return df
        except Exception as e:
            logger.error(f"Error loading data for date {date}: {e}")
            return pd.DataFrame()

    def _remove_invalid_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove invalid data from the dataframe. Only process data that have dish_category_ids in config."""
        valid_dish_category_ids = ConfigManager.get_instance().dish_category_ids  # set of int

        if 'dish_category_ids' not in df.columns:
            logger.warning(f"Column 'dish_category_ids' not found in dataframe. Skipping invalid data removal.")
            return df

        # Remove invalid data (including None values)
        df = df[df['dish_category_ids'].apply(
            lambda x: x is not None and all(dish_id in valid_dish_category_ids for dish_id in x))]

        return df

    def _download_s3_cluster_data(self, date: str) -> Tuple[str, bool]:
        """
        Download compressed cluster data from UMS S3 for a specific date.
        
        Args:
            date (str): Date in YYYY-MM-DD format
            
        Returns:
            Tuple[str, bool]: (date, success_status)
        """
        zip_key = f"{S3_UMS_DATA_PREFIX}/{date}/cluster_data_{date}.zip"

        try:
            # Check if zip file exists in S3
            if not ums_s3.object_exists(zip_key):
                return date, False

            ensure_dir_exists(HISTORICAL_CLUSTER_DATA_BASE_PATH)
            target_dir = os.path.join(HISTORICAL_CLUSTER_DATA_FOLDER, date)

            # Create a temporary path but don't create the file
            temp_zip = os.path.join(tempfile.gettempdir(), f"cluster_data_{date}.zip")

            try:
                # Download file
                logger.info(f"Downloading compressed data from S3 for date: {date}")
                ums_s3.download_file(zip_key, temp_zip)

                # Extract file only if download was successful
                if os.path.exists(temp_zip) and os.path.getsize(temp_zip) > 0:
                    logger.info(f"Extracting data for date: {date}")
                    extract_zip_archive(temp_zip, target_dir)
                    return date, True
                else:
                    logger.error(f"Download failed or empty file for date: {date}")
                    return date, False
            finally:
                # Cleanup temp file
                safe_delete_file(temp_zip)

        except Exception as e:
            logger.error(f"Failed to download and extract cluster data from S3 for date {date}: {e}")
            return date, False

    def _process_date_data(self, df: pd.DataFrame) -> None:
        """Process data for a specific date through the pipeline."""
        if df.empty:
            return

        try:
            # Parse addresses
            parsed_df = self.address_parser.parse_in_batch(df)

            # Generate embeddings
            embedded_df = self.embedding_generator.generate_embeddings_in_batch(parsed_df)

            # Cluster addresses
            with self.lock:
                self.address_clusterer.cluster_in_batch(embedded_df)
                self.address_clusterer.serialize(HISTORICAL_CLUSTER_DATA_FOLDER)

        except Exception as e:
            logger.error(f"Error processing data: {e}")

    def remove_date_data(self, date: str) -> None:
        """Remove data for a specific date from the clusters."""
        with self.lock:
            self.address_clusterer.remove_date_data(date)

    def process(self) -> None:
        """Process historical data for the last 7 days with optimized parallel downloading."""
        dates = self._get_date_range()
        logger.info(f"Processing historical data for dates: {dates}")
        today = datetime.now().strftime('%Y-%m-%d')

        # Step 1: Parallel download of S3 cluster data for all historical dates
        dates_with_s3_data = []
        dates_to_process = []

        with ThreadPoolExecutor(max_workers=min(len(dates), 10)) as executor:
            future_to_date = {
                executor.submit(self._download_s3_cluster_data, date): date
                for date in dates if date != today
            }

            for future in as_completed(future_to_date):
                date, success = future.result()
                if success:
                    dates_with_s3_data.append(date)
                else:
                    dates_to_process.append(date)

        # Step 2: Batch deserialize for dates with downloaded data
        if dates_with_s3_data:
            with self.lock:
                logger.info(f"Starting batch deserialization for dates: {dates_with_s3_data}")
                self.address_clusterer.deserialize(HISTORICAL_CLUSTER_DATA_FOLDER, dates_with_s3_data)

        # Step 3: Process remaining dates and today
        dates_to_process.append(today)  # Always process today's data
        for date in dates_to_process:
            df = self._load_data_for_date(date)
            df = self._remove_invalid_data(df)

            if not df.empty:
                self._process_date_data(df)
                logger.info(f"Completed processing for date: {date}")

                # Upload cluster data to UMS S3 for historical dates
                if date != today:
                    try:
                        local_date_path = os.path.join(HISTORICAL_CLUSTER_DATA_FOLDER, date)
                        if os.path.exists(local_date_path):
                            # Create zip archive
                            archive_name = f"cluster_data_{date}"
                            zip_path = create_zip_archive(local_date_path, archive_name)

                            # Upload zip file
                            s3_key = f"{S3_UMS_DATA_PREFIX}/{date}/{archive_name}.zip"
                            ums_s3.upload_file(zip_path, s3_key)
                            logger.info(f"Uploaded compressed cluster data to UMS S3 for date {date}")

                            # Cleanup temp zip file
                            safe_delete_file(zip_path)
                    except Exception as e:
                        logger.error(f"Failed to upload cluster data to UMS S3 for date {date}: {e}")

        logger.info("Historical data processing completed")

"""
Address Clustering Test Module

This module provides functions to cluster addresses using:
1. Address Parser (NER-based parsing for Vietnamese addresses)
2. Embedding Generator (BGE-M3 model for text embeddings)
3. Address Clusterer (Agglomerative clustering with cosine distance)

Usage:
    from src.processors.test_clustering import cluster_addresses
    
    addresses = ["123 Nguy<PERSON><PERSON>, Quận 7, TP.HCM", ...]
    address_df, cluster_dict = cluster_addresses(addresses)

Output:
    - address_df: DataFrame with columns ['address', 'cluster_id']
    - cluster_dict: Dictionary mapping cluster_id to list of addresses
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict, Tuple

from src.processors.address_parser import NERAddressParser
from src.processors.embedding_generator import AddressEmbeddingGenerator
from src.processors.address_clusterer import AgglomerativeAddressClusterer
from src.data_structures.embedding_encoder import BGEM3FlagModelEmbeddingEncoder
from src.common.consts import EMBEDDING_MODEL_NAME
from src.utils.logger import logger


def cluster_addresses(addresses: List[str], region: str = 'VN') -> <PERSON><PERSON>[pd.DataFrame, Dict[str, List[str]]]:
    """
    Cluster a list of addresses and return mappings.
    
    Args:
        addresses: List of address strings to cluster
        region: Region code for address parsing (default: 'VN')
        
    Returns:
        Tuple containing:
        - DataFrame with columns ['address', 'cluster_id'] mapping each address to its cluster
        - Dictionary mapping cluster_id to list of addresses in that cluster
    """

    logger.info(f"Starting clustering for {len(addresses)} addresses")

    # Step 1: Initialize processors
    address_parser = NERAddressParser(region=region)

    # Initialize embedding model with the configured model name
    logger.info(f"Initializing embedding model: {EMBEDDING_MODEL_NAME}")
    embedding_model = BGEM3FlagModelEmbeddingEncoder(EMBEDDING_MODEL_NAME, use_fp16=True)
    embedding_generator = AddressEmbeddingGenerator(model=embedding_model)

    address_clusterer = AgglomerativeAddressClusterer(
        address_parser=address_parser,
        embedding_generator=embedding_generator
    )

    # Step 2: Prepare input DataFrame
    # The parser expects specific column names
    input_df = pd.DataFrame({
        'delivery_address': addresses,
        'request_id': [f'test_{i}' for i in range(len(addresses))],
        'now_user_id': [i for i in range(len(addresses))],
        'restaurant_id': [1] * len(addresses),  # Using dummy restaurant_id
        'date_str': [datetime.now().strftime('%Y-%m-%d')] * len(addresses)
    })

    # Step 3: Parse addresses
    logger.info("Parsing addresses...")
    parsed_df = address_parser.parse_in_batch(input_df)

    if parsed_df.empty:
        logger.error("Address parsing returned empty results")
        return pd.DataFrame(), {}

    logger.info(f"Successfully parsed {len(parsed_df)} addresses")

    # Step 4: Generate embeddings
    logger.info("Generating embeddings...")
    embedded_df = embedding_generator.generate_embeddings_in_batch(parsed_df)

    if embedded_df.empty:
        logger.error("Embedding generation returned empty results")
        return pd.DataFrame(), {}

    logger.info(f"Successfully generated embeddings for {len(embedded_df)} addresses")

    # Add required columns for clustering
    embedded_df['request_id'] = [f'test_{i}' for i in range(len(embedded_df))]
    embedded_df['now_user_id'] = list(range(len(embedded_df)))
    embedded_df['restaurant_id'] = [1] * len(embedded_df)
    embedded_df['date_str'] = [datetime.now().strftime('%Y-%m-%d')] * len(embedded_df)

    # Step 5: Perform clustering
    logger.info("Clustering addresses...")
    address_clusterer.cluster_in_batch(embedded_df)

    # Step 6: Extract results
    # Get all unique group keys from the clusterer
    group_keys = set()
    for date_groups in address_clusterer.date_to_address_groups.values():
        group_keys.update(date_groups.keys())

    # Collect all clustering results
    address_to_cluster_mapping = []
    cluster_to_addresses_mapping = {}

    for group_key in group_keys:
        if group_key in address_clusterer.address_to_cluster_id:
            cluster_df = address_clusterer.address_to_cluster_id[group_key]

            # Add to address->cluster mapping
            for _, row in cluster_df.iterrows():
                address_to_cluster_mapping.append({
                    'address': row['address'],
                    'cluster_id': f"{group_key}_{row['cluster_id']}"  # Unique cluster ID across groups
                })

            # Add to cluster->addresses mapping
            if group_key in address_clusterer.cluster_id_to_addresses:
                for cluster_id, addr_list in address_clusterer.cluster_id_to_addresses[group_key].items():
                    unique_cluster_id = f"{group_key}_{cluster_id}"
                    cluster_to_addresses_mapping[unique_cluster_id] = addr_list

    # Create DataFrame from address->cluster mapping
    result_df = pd.DataFrame(address_to_cluster_mapping)

    logger.info(f"Clustering completed. Found {len(cluster_to_addresses_mapping)} clusters")

    return result_df, cluster_to_addresses_mapping


def test_clustering():
    """Test the clustering functionality with sample addresses."""

    # Sample Vietnamese addresses for testing
    sample_addresses = [
        " Số 27 ngõ 44, Phố Nguyễn Phúc Lai, Chợ Dừa, Đống Đa, Hà Nội, Đống Đa, Hà Nội",
        "1 Phố Trần Quang Diệu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "102 P. Mai Anh Tuấn, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "104 P. Mai Anh Tuấn, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "105 Hoàng Cầu Mới, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "112 Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "12 Ng. 32A P. Hào Nam, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "121 Ngõ 127 Phố Hào Nam, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "132/21 Phố Vũ Thạnh, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "15 84, 34/Hoàng Cầu, Tổ 74, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "180 Đ. La Thành, Chợ Dừa, Đống Đa, Hà Nội 11500, Việt Nam",
        "221 P. Mai Anh Tuấn, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "295 Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "304 Phố Mai Anh Tuấn, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "316 Đ. La Thành, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "32 Ngõ 170 Đường La Thành, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "32 Phố Đông Các, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "322 Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "324 Đê La Thành, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "34 Ng. 31 P. Hoàng Cầu, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "34 Ngõ 19 Phố Trần Quang Diệu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "35 Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "36 Hoàng Cầu, Chợ Dừa, Đống Đa, Đống Đa, Hà Nội",
        "36 Ng. 34 P. Hoàng Cầu, Chợ Dừa, Đống Đa, Hà Nội 100000, Việt Nam",
        "37 Hoàng Cầu Mới, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "37 Ngõ 266 Đường. La Thành, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "40 P. Võ Văn Dũng, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "43 Ng. 32A P. Hào Nam, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "49 P. Hào Nam, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "56 Phố Đông Các, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "598 Phố Nguyễn Hy Quang, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "65 Phố Võ Văn Dũng, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "68 Đ. La Thành, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "77 Hoàng Cầu Mới, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "82 P. Nguyễn Hy Quang, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "83 Phố Nguyễn Hy Quang, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "87 P. Hoàng Cầu, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "87 P. Võ Văn Dũng, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "9 P. Đông Các, Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "dalgom coffee, Phố Nguyễn Hy Quang, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Khách sạn Times Hoàng cầu, Ngõ 9 - Hoàng Cầu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Ngõ 46 Phố Hào Nam, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Ngõ 65 Hoàng Cầu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Nguyễn Phúc Lai, Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Phố Đông Các, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Phố Hào Nam, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Phố Hoàng Cầu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Phố Nguyễn Phúc Lai, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Số 16 Ng. 9 - Hoàng Cầu, Chợ Dừa, Đống Đa, Hà Nội",
        "Supermarket Hoang Cau, Phố Hoàng Cầu Ngõ 59 Phố Hoàng Cầu, Ô Chợ Dừa, Đống Đa, Hà Nội, Việt Nam",
        "Vũ Thạnh, P. Chợ Dừa, Đống Đa, Hà Nội",
    ]

    try:
        # Run clustering
        address_cluster_df, cluster_addresses_dict = cluster_addresses(sample_addresses)

        # Display results
        print("\n=== Address to Cluster Mapping ===")
        print(address_cluster_df.to_string(index=False))

        print("\n=== Cluster to Addresses Mapping ===")
        for cluster_id, addresses in cluster_addresses_dict.items():
            print(f"\nCluster {cluster_id}:")
            for addr in addresses:
                print(f"  - {addr}")

        return address_cluster_df, cluster_addresses_dict

    except Exception as e:
        logger.error(f"Error during clustering test: {e}")
        raise


if __name__ == "__main__":
    # Run the test
    test_clustering()

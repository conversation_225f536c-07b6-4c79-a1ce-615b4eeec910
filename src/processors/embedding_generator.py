from abc import ABC, abstractmethod

import numpy as np
import pandas as pd
from tenacity import retry, stop_after_attempt, wait_exponential

from src.common.consts import EMBEDDING_BATCH_SIZE, EMBEDDING_CHUNK_SIZE, EMBEDDING_MAX_LENGTH
from src.data_structures.embedding_encoder import AddressEmbeddingEncoder
from src.utils.logger import logger


class EmbeddingGenerator(ABC):
    @abstractmethod
    def generate_embedding(self, text: str) -> np.ndarray:
        pass

    @abstractmethod
    def generate_embeddings_in_batch(self, address_df: pd.DataFrame) -> pd.DataFrame:
        pass


class AddressEmbeddingGenerator(EmbeddingGenerator):
    def __init__(self, model: AddressEmbeddingEncoder, chunk_size: int = None, batch_size: int = None,
                 max_length: int = None):
        if chunk_size is None:
            self.chunk_size = EMBEDDING_CHUNK_SIZE

        if batch_size is None:
            self.batch_size = EMBEDDING_BATCH_SIZE

        if max_length is None:
            self.max_length = EMBEDDING_MAX_LENGTH

        self.model = model

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    def _generate(self, texts: list[str]) -> list[np.ndarray]:
        embeddings = self.model.encode(texts, batch_size=self.batch_size, max_length=self.max_length)
        corpus_embeddings = embeddings['dense_vecs']

        # Normalize non-zero vectors
        norms = np.linalg.norm(corpus_embeddings, axis=1, keepdims=True)
        norms[norms == 0] = 1  # Avoid division by zero
        corpus_embeddings = corpus_embeddings / norms

        return corpus_embeddings

    def generate_embedding(self, text: str) -> np.ndarray:
        return self._generate([text])[0]

    def generate_embeddings_in_batch(self, address_df: pd.DataFrame) -> pd.DataFrame:
        # Validate required columns
        required_columns = ['buyer_shipping_address', 'freetext_formatted_address']
        for column in required_columns:
            if column not in address_df.columns:
                raise ValueError(f"Column '{column}' is required but not found in the DataFrame.")

        address_df.drop_duplicates(subset='buyer_shipping_address', inplace=True)
        address_df.reset_index(drop=True, inplace=True)

        freetext_input_df = address_df[['buyer_shipping_address', 'freetext_formatted_address']]
        freetext_input_df = freetext_input_df[freetext_input_df.freetext_formatted_address != '']
        freetext_input_df.reset_index(drop=True, inplace=True)

        loop_count = int(freetext_input_df.shape[0]) // self.chunk_size
        corpus_embedding_list = []
        input_col = 'freetext_formatted_address'
        output_col = 'freetext_formatted_corpus_embeddings'

        for loop_index in range(loop_count + 1):
            index = min((loop_index + 1) * self.chunk_size, freetext_input_df.shape[0])
            batch_df = freetext_input_df[loop_index * self.chunk_size:index]

            # Convert to list of strings and ensure no None/nan values
            input_texts = [str(text) if pd.notna(text) else '' for text in batch_df[input_col].values]

            # Remove any whitespace
            input_texts = [text.strip() for text in input_texts]

            # Skip empty texts
            if not any(input_texts):
                logger.warning(
                    f"Batch {loop_index} contains only empty texts, skipping")
                continue

            try:
                corpus_embeddings = self._generate(input_texts)
                corpus_embedding_list.extend(corpus_embeddings)
            except Exception as e:
                logger.error(
                    f"Error generating embeddings for batch {loop_index}: {e}")
                raise e

        corpus_embedding_df = pd.DataFrame(data={output_col: corpus_embedding_list})
        freetext_output_emb_df = pd.concat([freetext_input_df, corpus_embedding_df], axis=1)[
            ['buyer_shipping_address', 'freetext_formatted_corpus_embeddings']]

        address_df = address_df.merge(freetext_output_emb_df, on='buyer_shipping_address', how='inner')

        return address_df

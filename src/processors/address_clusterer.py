from abc import ABC, abstractmethod
from collections import defaultdict
from threading import <PERSON><PERSON>ock

import time
import numpy as np
import pandas as pd
from sklearn.cluster import AgglomerativeClustering

from src.common.consts import CLUSTERING_DISTANCE_THRESHOLD
from src.data_structures.address_group import AddressGroup
from src.data_structures.cluster_result import ClusterResult
from src.data_structures.parsed_address import ParsedAddress
from src.data_structures.request import Request
from src.processors.address_parser import AddressParser
from src.processors.embedding_generator import EmbeddingGenerator
from src.utils.logger import logger
from src.utils.prometheus import (
    track_time, track_clustering_operation, track_address_processing,
    track_address_group_size, track_active_groups_count, track_active_dates_count,
    clustering_duration_seconds, address_parsing_duration_seconds, serialization_duration_seconds
)


class AddressClusterer(ABC):
    @abstractmethod
    def add_address_to_address_df(self, request: Request, parsed_address: ParsedAddress = None):
        pass

    @abstractmethod
    def cluster_new_request(self, request: Request) -> ClusterResult:
        pass

    @abstractmethod
    def cluster_in_batch(self, address_df: pd.DataFrame) -> list[str]:
        pass

    @abstractmethod
    def remove_date_data(self, date: str) -> None:
        pass

    @abstractmethod
    def serialize(self, base_folder: str) -> None:
        pass

    @abstractmethod
    def serialize_by_date(self, base_folder: str, date: str) -> None:
        pass

    @abstractmethod
    def deserialize(self, base_folder: str, dates: list[str]) -> None:
        pass


class AgglomerativeAddressClusterer(AddressClusterer):
    def __init__(self, address_parser: AddressParser, embedding_generator: EmbeddingGenerator):
        self.address_parser = address_parser
        self.embedding_generator = embedding_generator

        # Date → address group key → address group.
        self.date_to_address_groups: dict[str,
        dict[tuple[str, str, str], AddressGroup]] = {}

        # Address group key → cluster DF: address, cluster id.
        self.address_to_cluster_id: dict[tuple[str, str, str], pd.DataFrame] = {}

        # Address group key → Cluster id → list of addresses.
        self.cluster_id_to_addresses: dict[tuple[str, str, str], dict[int, list[str]]] = {}

        # Date → Address group key → order DF of req id, now user id, restaurant id, delivery address
        self.date_to_group_to_address_df: dict[str, dict[tuple[str, str, str], pd.DataFrame]] = {}

        self.clustering_model = AgglomerativeClustering(
            n_clusters=None,
            metric='cosine',
            linkage='average',
            distance_threshold=CLUSTERING_DISTANCE_THRESHOLD
        )

        self.lock = RLock()

    @track_time(clustering_duration_seconds.labels(operation_type='do_clustering'))
    def _do_clustering(self, group_key: tuple[str, str, str]) -> bool:
        with self.lock:
            address_groups = [
                group
                for date, groups in self.date_to_address_groups.items()
                for group in groups.values()
                if group.group_key == group_key
            ]

        addresses: list[ParsedAddress] = []
        embeddings: list[np.ndarray] = []
        for group in address_groups:
            addresses.extend(group.addresses)
            embeddings.extend(group.embeddings)

        # Track the number of addresses being clustered
        address_count = len(addresses)
        track_address_group_size(group_key, address_count)
        track_address_processing(operation_type='clustering_attempt')

        try:
            if len(embeddings) < 2:
                # No need to cluster groups with less than 2 addresses.
                cluster_labels = [0]
            else:
                cluster_labels = self.clustering_model.fit_predict(embeddings)

            # Track successful clustering operation
            track_clustering_operation('success', group_key)
        except Exception as e:
            logger.error(f"Error clustering addresses: {e}")
            # Track failed clustering operation
            track_clustering_operation('failed', group_key)
            return False

        # Create a new DF with address and cluster id columns.
        self.address_to_cluster_id[group_key] = pd.DataFrame({
            'address': [],
            'cluster_id': []
        }).astype({'address': str, 'cluster_id': int})
        self.cluster_id_to_addresses[group_key] = defaultdict(list)

        for address, cluster_id in zip(addresses, cluster_labels):
            self.address_to_cluster_id[group_key] = pd.concat([self.address_to_cluster_id[group_key], pd.DataFrame({
                'address': [address.freetext_formatted_address],
                'cluster_id': [cluster_id]
            })])
            self.cluster_id_to_addresses[group_key][cluster_id].append(address.freetext_formatted_address)

            # Track each address being processed
            track_address_processing(operation_type='clustered')

        # Track the number of clusters created
        num_clusters = len(self.cluster_id_to_addresses[group_key])
        logger.info(f"Created {num_clusters} clusters for group key {group_key} with {address_count} addresses")

        return True

    # Get address df in group in all dates.
    def _get_address_df_in_group(self, group_key: tuple[str, str, str]) -> pd.DataFrame:
        address_df = pd.DataFrame()
        for date, groups in self.date_to_group_to_address_df.items():
            for key, group in groups.items():
                if key == group_key:
                    address_df = pd.concat([address_df, group])
        return address_df

    def _build_result(self, group_key: tuple[str, str, str], address: str, request: Request) -> ClusterResult:
        cluster_id = self.address_to_cluster_id[group_key][self.address_to_cluster_id[group_key]['address'] == address][
            'cluster_id'].tolist()[0]

        address_to_cluster_id_in_cluster_df = self.address_to_cluster_id[group_key][
            self.address_to_cluster_id[group_key]['cluster_id'] == cluster_id]
        address_df_in_group = self._get_address_df_in_group(group_key).rename(
            columns={'freetext_formatted_address': 'address'})

        # Join address_df_in_group with address_to_cluster_id_in_cluster_df on address.
        address_df_in_cluster = pd.merge(
            address_df_in_group, address_to_cluster_id_in_cluster_df, on='address', how='inner')

        same_cluster_restaurant_user_count = \
            address_df_in_cluster[address_df_in_cluster['restaurant_id'] == request.restaurant_id][
                'now_user_id'].nunique()

        request_ids_in_cluster = address_df_in_cluster['request_id'].tolist()

        return ClusterResult(request.request_id, same_cluster_restaurant_user_count, request_ids_in_cluster)

    def add_address_to_address_df(self, request: Request, parsed_address: ParsedAddress = None):
        start_time = time.time()

        if parsed_address is None:
            # Track address parsing time separately
            parsing_start_time = time.time()
            parsed_address = self.address_parser.parse(request.delivery_address)
            parsing_time = time.time() - parsing_start_time
            address_parsing_duration_seconds.observe(parsing_time)

        can_parse = parsed_address is not None
        if can_parse:
            track_address_processing(operation_type='parsed')
        else:
            track_address_processing(operation_type='parse_failed')
            return

        request_date = request.date_str
        group_key = (parsed_address.state, parsed_address.city, parsed_address.district)

        with self.lock:
            if request_date not in self.date_to_group_to_address_df:
                self.date_to_group_to_address_df[request_date] = {}
                # Update active dates count
                track_active_dates_count(len(self.date_to_group_to_address_df))

            if group_key not in self.date_to_group_to_address_df[request_date]:
                self.date_to_group_to_address_df[request_date][group_key] = pd.DataFrame({
                    'request_id': [],
                    'now_user_id': [],
                    'restaurant_id': [],
                    'freetext_formatted_address': []
                }).astype(
                    {'request_id': str, 'now_user_id': int, 'restaurant_id': int, 'freetext_formatted_address': str})

                # Count total active groups across all dates
                total_groups = sum(len(groups) for groups in self.date_to_group_to_address_df.values())
                track_active_groups_count(total_groups)

            self.date_to_group_to_address_df[request_date][group_key] = pd.concat(
                [self.date_to_group_to_address_df[request_date][group_key], pd.DataFrame({
                    'request_id': [request.request_id],
                    'now_user_id': [request.now_user_id],
                    'restaurant_id': [request.restaurant_id],
                    'freetext_formatted_address': [parsed_address.freetext_formatted_address],
                })])

            # Track address being added
            track_address_processing(operation_type='added_to_df')

            # Track the time it took to add the address
            execution_time = time.time() - start_time
            clustering_duration_seconds.labels(operation_type='add_address').observe(execution_time)

    @track_time(clustering_duration_seconds.labels(operation_type='cluster_new_request'))
    def cluster_new_request(self, request: Request) -> ClusterResult:
        # Track address parsing time separately
        parsing_start_time = time.time()
        parsed_address = self.address_parser.parse(request.delivery_address)
        parsing_time = time.time() - parsing_start_time
        address_parsing_duration_seconds.observe(parsing_time)

        if parsed_address is None:
            # Failed to parse after retries, should skip.
            track_address_processing(operation_type='parse_failed')
            logger.warning(f"Failed to parse address for request {request.request_id}")
            return ClusterResult(request.request_id, 0)
        else:
            logger.info(
                f"Parsed address for request {request.request_id}: {parsed_address.freetext_formatted_address}, "
                f"group key: {parsed_address.state}, {parsed_address.city}, {parsed_address.district}"
            )
            track_address_processing(operation_type='parsed')

        # Track embedding generation time
        embedding_start_time = time.time()
        parsed_address.freetext_formatted_corpus_embeddings = self.embedding_generator.generate_embedding(
            parsed_address.freetext_formatted_address
        )
        embedding_time = time.time() - embedding_start_time
        logger.info(f"Generated embedding for request {request.request_id} in {embedding_time} seconds")
        clustering_duration_seconds.labels(operation_type='embedding_generation').observe(embedding_time)
        track_address_processing(operation_type='embedding_generated')

        request_date = request.date_str
        group_key = (parsed_address.state, parsed_address.city, parsed_address.district)

        with self.lock:
            if request_date not in self.date_to_address_groups:
                self.date_to_address_groups[request_date] = {}
                # Update active dates count
                track_active_dates_count(len(self.date_to_address_groups))

            if group_key not in self.date_to_address_groups[request_date]:
                self.date_to_address_groups[request_date][group_key] = AddressGroup(group_key)
                # Count total active groups across all dates
                total_groups = sum(len(groups) for groups in self.date_to_address_groups.values())
                track_active_groups_count(total_groups)

        self.add_address_to_address_df(request, parsed_address)

        # Check if group_key in self.address_to_cluster_id and the address is already clustered.
        address_already_clustered = group_key in self.address_to_cluster_id and parsed_address.freetext_formatted_address in \
                                    self.address_to_cluster_id[group_key].values
        if address_already_clustered:
            logger.info(
                f"Request {request.request_id}, address {parsed_address.freetext_formatted_address} already clustered, skipping")
            track_address_processing(operation_type='already_clustered')
            return self._build_result(group_key, parsed_address.freetext_formatted_address, request)

        with self.lock:
            logger.info(
                f"Request {request.request_id}, address {parsed_address.freetext_formatted_address} not clustered, adding to address group")
            self.date_to_address_groups[request_date][group_key].add_address(
                parsed_address,
                parsed_address.freetext_formatted_corpus_embeddings
            )

            # Track the size of the address group after adding the new address
            group_size = len(self.date_to_address_groups[request_date][group_key].addresses)
            track_address_group_size(group_key, group_size)
            track_address_processing(operation_type='added_to_group')

        success = self._do_clustering(group_key)
        if not success:
            logger.error(f"Error clustering addresses for group key {group_key}, request_id {request.request_id}")
            return ClusterResult(request.request_id, 1, [request.request_id])

        result = self._build_result(group_key, parsed_address.freetext_formatted_address, request)

        # Track the cluster size for this request
        if result.same_cluster_restaurant_user_count > 0:
            track_address_processing(operation_type='clustered_with_others')

        return result

    @track_time(clustering_duration_seconds.labels(operation_type='cluster_in_batch'))
    def cluster_in_batch(self, address_df: pd.DataFrame) -> None:
        # Check required columns
        required_columns = ['buyer_shipping_address', 'formatted_address', 'freetext_formatted_address', 'state',
                            'city', 'district',
                            'freetext_formatted_corpus_embeddings', 'date_str']
        for column in required_columns:
            if column not in address_df.columns:
                raise ValueError(f"Column {column} is required")

        # Track the number of addresses being processed in batch
        batch_size = len(address_df)
        logger.info(f"Processing batch of {batch_size} addresses")

        group_keys: list[tuple[str, str, str]] = []
        # Build up self.date_to_address_group.
        with self.lock:
            for index, row in address_df.iterrows():
                group_key: tuple[str, str, str] = (
                    row['state'], row['city'], row['district'])
                group_keys.append(group_key)

                date_str = row['date_str']
                request_id = row['request_id']
                now_user_id = row['now_user_id']
                restaurant_id = row['restaurant_id']
                freetext_formatted_address = row['freetext_formatted_address']

                if date_str not in self.date_to_address_groups:
                    self.date_to_address_groups[date_str] = {
                        group_key: AddressGroup(group_key)}
                    # Update active dates count
                    track_active_dates_count(len(self.date_to_address_groups))

                if group_key not in self.date_to_address_groups[date_str]:
                    self.date_to_address_groups[date_str][group_key] = AddressGroup(
                        group_key)
                    # Count total active groups across all dates
                    total_groups = sum(len(groups) for groups in self.date_to_address_groups.values())
                    track_active_groups_count(total_groups)

                parsed_address = ParsedAddress(row['buyer_shipping_address'], row['formatted_address'],
                                               freetext_formatted_address,
                                               row['state'], row['city'], row['district'],
                                               row['freetext_formatted_corpus_embeddings'])
                self.date_to_address_groups[date_str][group_key].add_address(
                    parsed_address,
                    parsed_address.freetext_formatted_corpus_embeddings
                )

                # Track each address being processed
                track_address_processing(operation_type='batch_processed')

                # Build self.date_to_group_to_address_df.
                if date_str not in self.date_to_group_to_address_df:
                    self.date_to_group_to_address_df[date_str] = {}

                if group_key not in self.date_to_group_to_address_df[date_str]:
                    self.date_to_group_to_address_df[date_str][group_key] = pd.DataFrame()

                self.date_to_group_to_address_df[date_str][group_key] = pd.concat(
                    [self.date_to_group_to_address_df[date_str][group_key], pd.DataFrame({
                        'request_id': [request_id],
                        'now_user_id': [now_user_id],
                        'restaurant_id': [restaurant_id],
                        'freetext_formatted_address': [freetext_formatted_address],
                    })])

                # Track the size of each address group
                group_size = len(self.date_to_address_groups[date_str][group_key].addresses)
                track_address_group_size(group_key, group_size)

        # Track unique group keys being processed
        unique_group_keys = set(group_keys)
        logger.info(f"Clustering {len(unique_group_keys)} unique address groups")

        for group_key in unique_group_keys:
            self._do_clustering(group_key)

    def remove_date_data(self, date: str) -> None:
        """
        Safely remove data for a specific date from the clusters.
        This includes removing the date's address group and updating related cluster mappings.

        Args:
            date (str): The date in YYYY-MM-DD format to remove data for
        """
        start_time = time.time()

        with self.lock:
            if date in self.date_to_address_groups:
                del self.date_to_address_groups[date]
                # Update active dates count
                track_active_dates_count(len(self.date_to_address_groups))
            else:
                logger.warning(f"No data found for date {date}")

            if date in self.date_to_group_to_address_df:
                del self.date_to_group_to_address_df[date]
            else:
                logger.warning(f"No data found for date {date}")

            # Count total active groups across all dates after removal
            total_groups = sum(len(groups) for groups in self.date_to_address_groups.values())
            track_active_groups_count(total_groups)

            logger.info(f"Successfully removed data for date {date}")

        # Track the time it took to remove the date data
        execution_time = time.time() - start_time
        clustering_duration_seconds.labels(operation_type='remove_date_data').observe(execution_time)

    @track_time(serialization_duration_seconds.labels(operation_type='serialize_by_date'))
    def serialize_by_date(self, base_folder: str, date: str) -> None:
        """
        Serialize clustering data for a specific date to disk.

        Args:
            base_folder (str): Base directory to store serialized data
            date (str): The date to serialize data for
        """
        import os
        import pickle
        import json

        with self.lock:
            if date not in self.date_to_address_groups:
                logger.warning(f"No data to serialize for date {date}")
                return

            # Create date folder
            date_folder = os.path.join(base_folder, date)
            os.makedirs(date_folder, exist_ok=True)

            # Save address groups for this date
            address_groups_path = os.path.join(date_folder, "address_groups.pkl")
            with open(address_groups_path, 'wb') as f:
                pickle.dump(self.date_to_address_groups[date], f)

            # Create a dedicated subfolder for DataFrames
            df_folder = os.path.join(date_folder, "dataframes")
            os.makedirs(df_folder, exist_ok=True)

            # Create a map to store group_key to filename mappings
            key_map = {}

            # Save DataFrames for this date
            if date in self.date_to_group_to_address_df:
                group_dfs = self.date_to_group_to_address_df[date]
                for idx, (group_key, df) in enumerate(group_dfs.items()):
                    filename = f"df_{idx}.parquet"
                    df_path = os.path.join(df_folder, filename)
                    df.to_parquet(df_path, index=False)

                    # Store the mapping between the index and the actual group_key
                    key_map[str(idx)] = list(group_key)

            # Save the key map
            key_map_path = os.path.join(date_folder, "key_map.json")
            with open(key_map_path, 'w') as f:
                json.dump(key_map, f)

            # Track the number of groups serialized
            num_groups = len(self.date_to_address_groups[date])
            logger.info(f"Successfully serialized {num_groups} address groups for date {date}")

    @track_time(serialization_duration_seconds.labels(operation_type='serialize'))
    def serialize(self, base_folder: str) -> None:
        """
        Serialize all clustering data to disk for later retrieval.

        Args:
            base_folder (str): Base directory to store serialized data
        """
        import os
        os.makedirs(base_folder, exist_ok=True)

        with self.lock:
            dates = list(self.date_to_address_groups.keys())
            total_dates = len(dates)
            logger.info(f"Serializing data for {total_dates} dates")

            for date in dates:
                self.serialize_by_date(base_folder, date)

            # Track the total number of dates and groups serialized
            total_groups = sum(len(groups) for groups in self.date_to_address_groups.values())
            logger.info(
                f"Successfully serialized {total_groups} address groups across {total_dates} dates to {base_folder}")

    @track_time(serialization_duration_seconds.labels(operation_type='deserialize'))
    def deserialize(self, base_folder: str, dates: list[str]) -> None:
        """
        Deserialize clustering data from disk.

        Args:
            base_folder (str): Base directory where data is stored
            dates (list[str]): List of dates to deserialize data for
        """
        import os
        import pickle
        import json

        logger.info(f"Deserializing data for {len(dates)} dates")
        successful_dates = 0
        total_groups_loaded = 0

        with self.lock:
            for date in dates:
                date_folder = os.path.join(base_folder, date)
                if not os.path.exists(date_folder):
                    logger.warning(f"No data found for date {date}")
                    continue

                # Load address groups
                address_groups_path = os.path.join(date_folder, "address_groups.pkl")
                if os.path.exists(address_groups_path):
                    with open(address_groups_path, 'rb') as f:
                        self.date_to_address_groups[date] = pickle.load(f)
                        # Track the number of groups loaded for this date
                        groups_loaded = len(self.date_to_address_groups[date])
                        total_groups_loaded += groups_loaded
                        track_address_processing(operation_type='group_deserialized')

                # Initialize dictionary for this date
                self.date_to_group_to_address_df[date] = {}

                # Load the key map
                key_map_path = os.path.join(date_folder, "key_map.json")
                if not os.path.exists(key_map_path):
                    logger.warning(f"Key map not found for date {date}")
                    continue

                with open(key_map_path, 'r') as f:
                    key_map = json.load(f)

                # Load DataFrames using the key map
                df_folder = os.path.join(date_folder, "dataframes")
                if not os.path.exists(df_folder):
                    logger.warning(f"DataFrames folder not found for date {date}")
                    continue

                dfs_loaded = 0
                for idx_str, key_list in key_map.items():
                    # Convert index back to string and construct filename
                    filename = f"df_{idx_str}.parquet"
                    df_path = os.path.join(df_folder, filename)

                    if os.path.exists(df_path):
                        # Convert list back to tuple for group_key
                        group_key = tuple[str, str, str](key_list)
                        self.date_to_group_to_address_df[date][group_key] = pd.read_parquet(df_path)
                        dfs_loaded += 1
                        track_address_processing(operation_type='df_deserialized')
                    else:
                        logger.warning(f"DataFrame file {filename} not found for date {date}")

                logger.info(f"Loaded {dfs_loaded} dataframes for date {date}")
                successful_dates += 1

            # Update active dates and groups counts
            track_active_dates_count(len(self.date_to_address_groups))
            total_groups = sum(len(groups) for groups in self.date_to_address_groups.values())
            track_active_groups_count(total_groups)

            # Rebuild clusters for all group keys
            group_keys = set()
            for date, groups in self.date_to_address_groups.items():
                for group_key in groups.keys():
                    group_keys.add(group_key)

            logger.info(f"Rebuilding clusters for {len(group_keys)} unique group keys")
            for group_key in group_keys:
                self._do_clustering(group_key)

            logger.info(
                f"Successfully deserialized {total_groups_loaded} address groups for {successful_dates}/{len(dates)} dates")

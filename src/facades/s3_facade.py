import os
from datetime import datetime
from typing import List, Optional
import boto3
from botocore.client import Config
from tenacity import retry, stop_after_attempt, wait_exponential

from src.common.consts import (
    S3_REGION,
    S3_ENDPOINT_URL,
    S3_BUCKET_NAME,
    S3_ORDER_DATA_PREFIX,
    S3_ORDER_DATA_PATH,
    S3_UMS_REGION,
    S3_UMS_ENDPOINT_URL,
    S3_UMS_BUCKET_NAME,
    S3_UMS_DATA_PREFIX,
    S3_UMS_LOCAL_DATA_PATH,
    S3_ORDER_AWS_ACCESS_KEY_ID,
    S3_ORDER_AWS_SECRET_ACCESS_KEY,
    S3_UMS_AWS_ACCESS_KEY_ID,
    S3_UMS_AWS_SECRET_ACCESS_KEY,
)
from src.utils.logger import logger


class S3Facade:
    def __init__(self, region: str, endpoint_url: str, bucket: str, data_prefix: str, data_local_path: str,
                 aws_access_key_id: str, aws_secret_access_key: str):
        """Initialize S3 client with the configured settings."""
        session = boto3.session.Session(region_name=region, aws_access_key_id=aws_access_key_id,
                                        aws_secret_access_key=aws_secret_access_key)
        self.s3_client = session.client(
            's3',
            endpoint_url=endpoint_url,
            config=Config(signature_version='s3v4')
        )

        self.bucket = bucket
        self.data_prefix = data_prefix
        self.data_local_path = data_local_path

    def object_exists(self, key: str) -> bool:
        """
        Check if an object exists in S3.

        Args:
            key (str): The S3 object key

        Returns:
            bool: True if the object exists, False otherwise
        """
        try:
            self.s3_client.head_object(Bucket=self.bucket, Key=key)
            return True
        except Exception as e:
            logger.error(f"Error checking if object exists {key}: {str(e)}")
            return False


    def list_objects(self, prefix: str, exclude_dir: bool = True) -> List[str]:
        """
        List all objects in the bucket with the given prefix.

        Args:
            prefix (str): The prefix to filter objects
            exclude_dir (bool, optional): Whether to exclude directories. Defaults to True.

        Returns:
            List[str]: List of object keys
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix
            )
            if 'Contents' not in response:
                return []

            if exclude_dir:
                return [obj['Key'] for obj in response['Contents'] if not obj['Key'].endswith('/')]

            return [obj['Key'] for obj in response['Contents']]
        except Exception as e:
            raise Exception(
                f"Error listing objects with prefix {prefix}: {str(e)}")

    def download_file(self, s3_key: str, local_path: str, overwrite: bool = False) -> str:
        """
        Download a file from S3 to local storage.

        Args:
            s3_key (str): The S3 object key
            local_path (str): Local path to save the file
            overwrite (bool): Whether to overwrite existing file

        Returns:
            str: Path to the downloaded file
            
        Raises:
            Exception: If download fails or file size mismatch
        """
        try:
            if not overwrite and os.path.exists(local_path):
                return local_path

            # First check if the object exists and get its size
            try:
                obj = self.s3_client.head_object(Bucket=self.bucket, Key=s3_key)
                expected_size = obj['ContentLength']
            except Exception as e:
                raise Exception(f"Error checking S3 object {s3_key}: {str(e)}")

            # Ensure directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # Download the file
            self.s3_client.download_file(
                Bucket=self.bucket,
                Key=s3_key,
                Filename=local_path
            )

            # Verify file size after download
            actual_size = os.path.getsize(local_path)
            if actual_size == 0:
                raise Exception(f"Downloaded file is empty: {local_path}")
            if actual_size != expected_size:
                raise Exception(
                    f"File size mismatch for {s3_key}. Expected: {expected_size}, Got: {actual_size}")

            logger.info(f"Successfully downloaded {s3_key} to {local_path} ({actual_size} bytes)")
            return local_path

        except Exception as e:
            # Clean up partial/failed download
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                except Exception:
                    pass  # Ignore cleanup errors
            raise Exception(f"Error downloading file {s3_key}: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def download_order_data_by_date(self, date: datetime, output_dir: str = None, overwrite: bool = False) -> List[str]:
        """
        Download all order data files for a specific date.

        Args:
            date (datetime): The date to download data for
            output_dir (str, optional): Directory to save downloaded files. Defaults to self.order_data_path
            overwrite (bool, optional): Whether to overwrite existing files. Defaults to False.

        Returns:
            List[str]: List of local file paths for downloaded files
        """
        if output_dir is None:
            output_dir = self.data_local_path

        date_str = date.strftime('%Y-%m-%d')
        prefix = f"{self.data_prefix}/grass_date={date_str}"

        # List all files for the date
        s3_object_keys = self.list_objects(prefix)
        if not s3_object_keys:
            logger.info(f"No objects in {prefix}")
            return []

        # Download each file
        local_paths = []
        for obj_key in s3_object_keys:
            filename = os.path.basename(obj_key)
            local_path = os.path.join(output_dir, date_str, filename)
            downloaded_path = self.download_file(
                obj_key, local_path, overwrite=overwrite)
            local_paths.append(downloaded_path)

        return local_paths

    def get_object(self, key: str) -> bytes:
        """
        Get an object's content from S3.

        Args:
            key (str): The S3 object key

        Returns:
            bytes: The object's content
        """
        try:
            response = self.s3_client.get_object(Bucket=self.bucket, Key=key)
            return response['Body'].read()
        except Exception as e:
            raise Exception(f"Error getting object {key}: {str(e)}")

    def delete_object(self, key: str) -> None:
        """
        Delete an object from S3.

        Args:
            key (str): The S3 object key to delete
        """
        try:
            self.s3_client.delete_object(Bucket=self.bucket, Key=key)
        except Exception as e:
            raise Exception(f"Error deleting object {key}: {str(e)}")

    def copy_object(self, source_key: str, dest_key: str) -> None:
        """
        Copy an object within the same bucket.

        Args:
            source_key (str): Source object key
            dest_key (str): Destination object key
        """
        try:
            copy_source = {
                'Bucket': self.bucket,
                'Key': source_key
            }
            self.s3_client.copy_object(
                CopySource=copy_source,
                Bucket=self.bucket,
                Key=dest_key
            )
        except Exception as e:
            raise Exception(
                f"Error copying object from {source_key} to {dest_key}: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def upload_file(self, local_path: str, s3_key: str, overwrite: bool = False) -> str:
        """
        Upload a file to S3.

        Args:
            local_path (str): Path to the local file
            s3_key (str): S3 key to upload the file to
            overwrite (bool, optional): Whether to overwrite existing file. Defaults to False.

        Returns:
            str: S3 key of the uploaded file
        """
        try:
            if not overwrite and self.object_exists(s3_key):
                return s3_key

            self.s3_client.upload_file(local_path, self.bucket, s3_key)
            logger.info(f"Successfully uploaded {local_path} to {s3_key}")
            return s3_key
        except Exception as e:
            raise Exception(f"Error uploading file {local_path}: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def upload_file_by_date(self, local_path: str, date: datetime, custom_prefix: Optional[str] = None) -> str:
        """
        Upload a single file to S3 with date-based organization.

        Args:
            local_path (str): Path to the local file
            date (datetime): Date to organize the file under
            custom_prefix (str, optional): Custom prefix instead of default data_prefix

        Returns:
            str: S3 key of the uploaded file
        """
        try:
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"File not found: {local_path}")

            date_str = date.strftime('%Y-%m-%d')
            prefix = custom_prefix if custom_prefix else self.data_prefix
            filename = os.path.basename(local_path)
            s3_key = f"{prefix}/{date_str}/{filename}"

            self.s3_client.upload_file(local_path, self.bucket, s3_key)
            logger.info(f"Successfully uploaded {local_path} to {s3_key}")
            return s3_key
        except Exception as e:
            raise Exception(f"Error uploading file {local_path}: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def upload_directory_by_date(self, local_dir: str, date: datetime, custom_prefix: Optional[str] = None) -> List[
        str]:
        """
        Upload all files from a directory to S3 with date-based organization.

        Args:
            local_dir (str): Path to the local directory
            date (datetime): Date to organize the files under
            custom_prefix (str, optional): Custom prefix instead of default data_prefix

        Returns:
            List[str]: List of S3 keys of uploaded files
        """
        try:
            if not os.path.isdir(local_dir):
                raise NotADirectoryError(f"Not a directory: {local_dir}")

            date_str = date.strftime('%Y-%m-%d')
            prefix = custom_prefix if custom_prefix else self.data_prefix
            uploaded_keys = []

            for root, _, files in os.walk(local_dir):
                for filename in files:
                    local_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(local_path, local_dir)
                    s3_key = f"{prefix}/{date_str}/{relative_path}"

                    self.s3_client.upload_file(local_path, self.bucket, s3_key)
                    uploaded_keys.append(s3_key)
                    logger.info(f"Uploaded {local_path} to {s3_key}")

            return uploaded_keys
        except Exception as e:
            raise Exception(f"Error uploading directory {local_dir}: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def upload_files_by_date_batch(self, local_paths: List[str], date: datetime,
                                   custom_prefix: Optional[str] = None) -> List[str]:
        """
        Upload multiple specified files to S3 with date-based organization in batch.

        Args:
            local_paths (List[str]): List of local file paths
            date (datetime): Date to organize the files under
            custom_prefix (str, optional): Custom prefix instead of default data_prefix

        Returns:
            List[str]: List of S3 keys of uploaded files
        """
        try:
            date_str = date.strftime('%Y-%m-%d')
            prefix = custom_prefix if custom_prefix else self.data_prefix
            uploaded_keys = []

            for local_path in local_paths:
                if not os.path.exists(local_path):
                    logger.warning(f"File not found, skipping: {local_path}")
                    continue

                filename = os.path.basename(local_path)
                s3_key = f"{prefix}/{date_str}/{filename}"

                self.s3_client.upload_file(local_path, self.bucket, s3_key)
                uploaded_keys.append(s3_key)
                logger.info(f"Uploaded {local_path} to {s3_key}")

            return uploaded_keys
        except Exception as e:
            raise Exception(f"Error uploading batch files: {str(e)}")

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=60))
    def upload_with_metadata_by_date(self, local_path: str, date: datetime,
                                     metadata: dict, custom_prefix: Optional[str] = None) -> str:
        """
        Upload a file to S3 with custom metadata and date-based organization.

        Args:
            local_path (str): Path to the local file
            date (datetime): Date to organize the file under
            metadata (dict): Custom metadata to attach to the file
            custom_prefix (str, optional): Custom prefix instead of default data_prefix

        Returns:
            str: S3 key of the uploaded file
        """
        try:
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"File not found: {local_path}")

            date_str = date.strftime('%Y-%m-%d')
            prefix = custom_prefix if custom_prefix else self.data_prefix
            filename = os.path.basename(local_path)
            s3_key = f"{prefix}/{date_str}/{filename}"

            self.s3_client.upload_file(
                Filename=local_path,
                Bucket=self.bucket,
                Key=s3_key,
                ExtraArgs={'Metadata': metadata}
            )
            logger.info(f"Uploaded {local_path} to {s3_key} with metadata")
            return s3_key
        except Exception as e:
            raise Exception(f"Error uploading file with metadata {local_path}: {str(e)}")


s3_facade = S3Facade(
    region=S3_REGION,
    endpoint_url=S3_ENDPOINT_URL,
    bucket=S3_BUCKET_NAME,
    data_prefix=S3_ORDER_DATA_PREFIX,
    data_local_path=S3_ORDER_DATA_PATH,
    aws_access_key_id=S3_ORDER_AWS_ACCESS_KEY_ID,
    aws_secret_access_key=S3_ORDER_AWS_SECRET_ACCESS_KEY
)

ums_s3 = S3Facade(
    region=S3_UMS_REGION,
    endpoint_url=S3_UMS_ENDPOINT_URL,
    bucket=S3_UMS_BUCKET_NAME,
    data_prefix=S3_UMS_DATA_PREFIX,
    data_local_path=S3_UMS_LOCAL_DATA_PATH,
    aws_access_key_id=S3_UMS_AWS_ACCESS_KEY_ID,
    aws_secret_access_key=S3_UMS_AWS_SECRET_ACCESS_KEY
)

if __name__ == "__main__":
    local_files = s3_facade.download_order_data_by_date(
        date=datetime(2025, 3, 13),
    )

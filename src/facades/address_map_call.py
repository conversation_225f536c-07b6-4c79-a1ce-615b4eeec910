import json
import os
from concurrent.futures import ThreadPoolExecutor

import pandas as pd
import requests
from ratelimit import limits, sleep_and_retry
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_result
from tqdm import tqdm

from src.utils.config_manager import ConfigManager
from src.utils.logger import logger

ner_url = "https://map.shopee.io/api/v4/geo/ner"
ner_url_batch = "https://map.shopee.io/api/v4/geo/batch_ner"
category_url = "https://map.shopee.io/api/v4/geo/category_identify"

NUM_WORKERS = 5
NUM_DATA = 100


def _has_missing_admin_divisions_in_batch(batch_results) -> bool:
    """
    Check if all results in the batch have missing admin division fields.

    Args:
        batch_results: List of NER API results from batch call

    Returns:
        True if all results have missing admin divisions, False otherwise
    """
    if not batch_results:
        return True

    for result in batch_results:
        if not result or 'entities' not in result:
            continue

        # Check if this result has any admin divisions
        has_admin_divisions = False
        for entity in result['entities']:
            if entity['type'] in ['admin_division_1', 'admin_division_2', 'admin_division_3']:
                # Check if the value is not empty after stripping
                value = entity.get('target_value', '')
                if value and str(value).strip():
                    has_admin_divisions = True
                    break

        # If any result has admin divisions, return False (don't retry)
        if has_admin_divisions:
            return False

    # All results are missing admin divisions, should retry
    return True


def _has_missing_admin_divisions_in_api_response(resp_data) -> bool:
    """
    Check if the API response is missing admin division fields.

    Args:
        resp_data: The NER API response data

    Returns:
        True if admin divisions are missing or empty, False otherwise
    """
    if not resp_data or 'entities' not in resp_data:
        return True

    # Check if any admin divisions are present and non-empty
    for entity in resp_data['entities']:
        if entity['type'] in ['admin_division_1', 'admin_division_2', 'admin_division_3']:
            # Check if the value is not empty after stripping
            value = entity.get('target_value', '')
            if value and str(value).strip():
                return False

    # No admin divisions found, should retry
    return True


@sleep_and_retry
@limits(calls=50, period=1)
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=0.1, min=0, max=1))
def ner_api_call(data, admin_dict, country):
    # Build existing_entities dict, excluding empty values
    existing_entities = {}
    if admin_dict.get('state') and admin_dict['state'].strip():
        existing_entities["admin_division_1"] = admin_dict['state']
    if admin_dict.get('city') and admin_dict['city'].strip():
        existing_entities["admin_division_2"] = admin_dict['city']
    if admin_dict.get('district') and admin_dict['district'].strip():
        existing_entities["admin_division_3"] = admin_dict['district']

    request_data = {
        "data": data,
        "use_case": "business.security",
        "country": country
    }

    # Only add existing_entities if we have at least one non-empty value
    if existing_entities:
        request_data["existing_entities"] = existing_entities

    data = request_data
    resp = requests.post(url=ner_url, data=json.dumps(data))
    if resp.status_code != 200:
        print(f"status code is not 200, but {resp.status_code}")
        raise Exception(f"status code is not 200, but {resp.status_code}")

    resp_data = resp.json()['data']

    # Log if admin divisions are missing for retry tracking
    if _has_missing_admin_divisions_in_api_response(resp_data):
        logger.warning(
            f'NER API call returned empty admin divisions for address: {request_data.get("data", "")}')

        raise Exception("Empty admin divisions")

    return resp_data


segment_names_th = ['place_name', 'house_number', 'room', 'floor', 'building', 'group', 'village', 'trok', 'alley',
                    'Intersection', 'street', 'subdistrict', 'admin_division_2', 'admin_division_1', 'zipcode']
freetext_segment_names_th = ['place_name', 'house_number', 'room', 'floor', 'building', 'group', 'village', 'trok',
                             'alley', 'Intersection', 'street']

segment_names_id = ['zipcode', 'admin_division_1', 'admin_division_2', 'admin_division_3', 'admin_division_4',
                    'admin_division_5', 'house_number', 'block', 'rt', 'rw', 'place_name', 'jalan', 'gang', 'floor',
                    'room', 'unit']

segment_names_vn = ['admin_division_1', 'admin_division_2', 'admin_division_3', 'floor', 'lot', 'block', 'room_no',
                    'group', 'alley', 'street', 'street_name', 'street_number', 'neighborhood', 'place_name',
                    'building_number', 'house_number']

segment_names_my = ['place_name', 'room_no', 'floor', 'block', 'house_number', 'lorong', 'lorong_name', 'lorong_number',
                    'jalan', 'jalan_name', 'jalan_number', 'admin_division_4', 'admin_division_3', 'admin_division_2',
                    'admin_division_1', 'zipcode']


def extract_address_ner(row, country):
    data = row['buyer_shipping_address']
    admin_dict = {
        'state': row['buyer_shipping_address_state'],
        'city': row['buyer_shipping_address_city'],
        'district': row['buyer_shipping_address_district']
    }

    resp_data = ner_api_call(data, admin_dict, country)

    if resp_data is None or 'entities' not in resp_data:
        row['address_segment'] = ''
        row['freetext_formatted_address'] = ''
        row['formatted_address'] = ''
        return row

    segment_dict = {}
    segment_names = []
    freetext_segment_names = []
    if country == 'TH':
        segment_names = segment_names_th
        freetext_segment_names = freetext_segment_names_th
    elif country == 'ID':
        segment_names = segment_names_id
    elif country == 'VN':
        segment_names = segment_names_vn
    elif country == 'MY':
        segment_names = segment_names_my
    for entity in resp_data['entities']:
        if entity['type'] in segment_names:
            segment_dict[entity['type']] = entity['target_value']
    row['address_segment'] = json.dumps(segment_dict,
                                        ensure_ascii=False)  # This ensures that the Thai (or other non-ASCII) characters are not converted to Unicode escape sequences. Instead, they are preserved in their original form in the JSON string.
    if country == 'TH':
        freetext_formatted_address = ''
        for name in freetext_segment_names:
            freetext_formatted_address += segment_dict[name] + \
                                          ' ' if name in segment_dict else ''
    elif country in ['ID', 'VN', 'MY']:
        freetext_formatted_address = resp_data['partial_formatted_address']
    row['freetext_formatted_address'] = freetext_formatted_address.strip()

    row['formatted_address'] = resp_data['formatted_address']

    return row


# Function to process the DataFrame using multi-threading
def call_address_ner(df, country):
    logger.info(f"Processing {len(df)} addresses through NER API")
    success_count = 0
    empty_result_count = 0

    with ThreadPoolExecutor(max_workers=5) as executor:
        # Process each row in parallel
        results = list(
            tqdm(executor.map(extract_address_ner, df.to_dict('records'), [country] * len(df), ), total=df.shape[0]))

    # Count successful and empty results
    for result in results:
        if result['freetext_formatted_address'] != '':
            success_count += 1
        else:
            empty_result_count += 1

    logger.info(f"NER API processing completed:")
    logger.info(f"- Total addresses processed: {len(df)}")
    logger.info(f"- Successful results: {success_count}")
    logger.info(f"- Empty results: {empty_result_count}")

    # Create a new DataFrame from the processed results
    processed_df = pd.DataFrame(results)

    return processed_df


@sleep_and_retry
@limits(calls=5, period=0.5)
def category_identify_api_call(admin_dict, country):
    try:
        data = {
            "address": admin_dict['buyer_shipping_address'],
            "use_case": "business.security",
            "country": country,
            "place_name": admin_dict['place_name']
        }
        with requests.Session() as session:
            resp = session.get(url=category_url, params=data)
        if resp.status_code != 200:
            print(f"status code is not 200, but {resp.status_code}")
            return -1
        #     print(resp.json())
        resp_data = resp.json()['data']
    except Exception as e:
        logger.error(f'input data:{data}, error: {e}')
        return None
    return resp_data


def extract_address_category(row, country):
    admin_dict = {}
    admin_dict['buyer_shipping_address'] = row['buyer_shipping_address']
    admin_dict['place_name'] = row['place_name']
    resp_data = category_identify_api_call(admin_dict, country)
    if resp_data is None:
        row['category'] = ''
        row['confidence'] = 0
    else:
        row['category'] = resp_data['category']
        row['confidence'] = resp_data['confidence']
    return row


# Function to process the DataFrame using multi-threading
def call_address_category(df, country):
    with ThreadPoolExecutor(max_workers=10) as executor:
        # Process each row in parallel
        results = list(
            tqdm(executor.map(extract_address_category, df.to_dict('records'), [country] * len(df), ),
                 total=df.shape[0]))

    # Create a new DataFrame from the processed results
    processed_df = pd.DataFrame(results)

    return processed_df


@sleep_and_retry
@limits(calls=3, period=1)
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=0.1, min=0, max=1))
def ner_batch_api_call(addresses, country):
    data = {
        "data": [{"address": addr} for addr in addresses],
        "use_case": "BE.antifraud.af_address_clustering",
        "country": country
    }
    resp = requests.post(url=ner_url_batch, json=data)
    if resp.status_code != 200:
        logger.error(f"status code is not 200, but {resp.status_code}")
        raise Exception(f"status code is not 200, but {resp.status_code}")

    resp_data = resp.json()['data']

    # Log if admin divisions are missing for retry tracking
    if _has_missing_admin_divisions_in_batch(resp_data):
        logger.warning(
            f'Batch NER processing returned empty admin divisions for {len(addresses)} addresses')

        raise Exception("Empty admin divisions")

    return resp_data


def process_batch_results(batch_results, original_addresses, country):
    """Process a batch of NER results and convert to DataFrame rows"""
    processed_rows = []

    # Select appropriate segment names based on country
    if country == 'TH':
        segment_names = segment_names_th
    elif country == 'ID':
        segment_names = segment_names_id
    elif country == 'VN':
        segment_names = segment_names_vn
    elif country == 'MY':
        segment_names = segment_names_my
    else:
        logger.error(f"Unsupported country: {country}")
        segment_names = []

    for result, orig_address in zip(batch_results, original_addresses):
        row = {'buyer_shipping_address': orig_address}

        if not result or 'entities' not in result:
            row['address_segment'] = ''
            row['freetext_formatted_address'] = ''
            row['formatted_address'] = ''
            processed_rows.append(row)
            continue

        # Process segments based on country-specific rules
        segment_dict = {}
        for entity in result['entities']:
            if entity['type'] in segment_names:
                segment_dict[entity['type']] = entity['target_value']

        row['address_segment'] = json.dumps(segment_dict, ensure_ascii=False)
        row['freetext_formatted_address'] = result['partial_formatted_address']
        row['formatted_address'] = result['formatted_address']
        processed_rows.append(row)

    return processed_rows


def process_address_batch(addresses, country):
    """Process a single batch of addresses"""
    try:
        results = ner_batch_api_call(addresses, country)
        return process_batch_results(results, addresses, country)
    except Exception as e:
        logger.error(f'Error in batch call: {e}')
        return [{'buyer_shipping_address': addr,
                 'address_segment': '',
                 'freetext_formatted_address': '',
                 'formatted_address': ''} for addr in addresses]


def call_address_ner_batch(df, country, batch_size=None):
    """Process addresses using batch NER API with multi-threading"""
    logger.info(f"Processing {len(df)} addresses through batch NER API")

    if batch_size is None:
        batch_size = ConfigManager.get_instance().ner_batch_size

    # Split addresses into batches
    addresses = df['buyer_shipping_address'].tolist()
    batches = [addresses[i:i + batch_size]
               for i in range(0, len(addresses), batch_size)]

    all_results = []
    success_count = 0
    empty_result_count = 0

    with ThreadPoolExecutor(max_workers=NUM_WORKERS) as executor:
        # Process each batch in parallel
        batch_results = list(tqdm(
            executor.map(lambda batch: process_address_batch(batch, country), batches),
            total=len(batches),
            desc="Processing batches"
        ))

        # Flatten results
        for batch in batch_results:
            all_results.extend(batch)

    # Count successful and empty results
    for result in all_results:
        if result['freetext_formatted_address'] != '':
            success_count += 1
        else:
            empty_result_count += 1

    logger.info(f"Batch NER API processing completed:")
    logger.info(f"- Total addresses processed: {len(df)}")
    logger.info(f"- Successful results: {success_count}")
    logger.info(f"- Empty results: {empty_result_count}")

    # Create a new DataFrame from the processed results
    processed_df = pd.DataFrame(all_results)

    return processed_df

import os
from typing import Optional

import redis
from tenacity import retry, stop_after_attempt, wait_exponential

from src.common.consts import INSTANCE_ID, DISTRIBUTED_LOCK_PREFIX, REDIS_HOST, REDIS_PORT, REDIS_USERNAME, \
    REDIS_PASSWORD, REDIS_TTL_SECONDS
from src.utils.logger import logger


class RedisFacade:
    def __init__(self, host: str, port: int, username: str, password: str):
        """Initialize Redis client with environment variables."""
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            username=username,
            password=password,
            decode_responses=True
        )
        self.instance_id = INSTANCE_ID
        self.lock_prefix = DISTRIBUTED_LOCK_PREFIX
        self.lock_ttl = REDIS_TTL_SECONDS

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    def acquire_lock(self, request_id: str) -> bool:
        """
        Acquire a distributed lock for the given request_id.
        
        Args:
            request_id: The unique identifier for the request
            
        Returns:
            bool: True if lock was acquired, False otherwise
        """
        lock_key = f"{self.lock_prefix}{request_id}"
        try:
            # SET NX (Only set if key doesn't exist) with expiration
            acquired = self.redis_client.set(
                name=lock_key,
                value=self.instance_id,
                nx=True,
                ex=self.lock_ttl
            )
            if acquired:
                logger.debug(f"Lock acquired for request_id: {request_id}")
            else:
                logger.debug(f"Failed to acquire lock for request_id: {request_id}")
            return bool(acquired)
        except Exception as e:
            logger.error(f"Error acquiring lock for request_id {request_id}: {str(e)}")
            return False

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10))
    def release_lock(self, request_id: str) -> bool:
        """
        Release the distributed lock for the given request_id.
        Only releases if the lock is owned by this instance.
        
        Args:
            request_id: The unique identifier for the request
            
        Returns:
            bool: True if lock was released, False otherwise
        """
        lock_key = f"{self.lock_prefix}{request_id}"
        try:
            # First check if we own the lock
            lock_owner = self.redis_client.get(lock_key)
            if lock_owner != self.instance_id:
                logger.warning(
                    f"Cannot release lock for request_id {request_id}. "
                    f"Lock is owned by {lock_owner}, not {self.instance_id}"
                )
                return False

            # Delete the lock if we own it
            deleted = self.redis_client.delete(lock_key)
            if deleted:
                logger.debug(f"Lock released for request_id: {request_id}")
            else:
                logger.warning(f"Lock key not found for request_id: {request_id}")
            return bool(deleted)
        except Exception as e:
            logger.error(f"Error releasing lock for request_id {request_id}: {str(e)}")
            return False

    def get_lock_owner(self, request_id: str) -> Optional[str]:
        """
        Get the instance ID of the current lock owner.
        
        Args:
            request_id: The unique identifier for the request
            
        Returns:
            Optional[str]: The instance ID of the lock owner if lock exists, None otherwise
        """
        lock_key = f"{self.lock_prefix}{request_id}"
        try:
            return self.redis_client.get(lock_key)
        except Exception as e:
            logger.error(f"Error getting lock owner for request_id {request_id}: {str(e)}")
            return None


redis_facade = RedisFacade(
    host=REDIS_HOST,
    port=REDIS_PORT,
    username=REDIS_USERNAME,
    password=REDIS_PASSWORD,
)

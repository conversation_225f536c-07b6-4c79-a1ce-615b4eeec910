from typing import Dict, Optional
import numpy as np
from src.data_structures.parsed_address import ParsedAddress


class AddressGroup:
    def __init__(self, group_key: tuple[str, str, str]):
        self.group_key = group_key
        self.addresses: list[<PERSON>rse<PERSON><PERSON>dd<PERSON>] = []
        self.embeddings: list[np.ndarray] = []

    def add_address(self, address: Parse<PERSON>Address, embedding: Optional[np.ndarray] = None) -> None:
        self.addresses.append(address)
        if embedding is not None:
            self.embeddings.append(embedding)

    def get_location_info(self) -> Dict[str, str]:
        return {
            'district': self.group_key[0],
            'city': self.group_key[1],
            'state': self.group_key[2]
        }

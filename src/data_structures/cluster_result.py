class ClusterResult:
    def __init__(self, request_id: str, same_cluster_restaurant_user_count: int, request_ids_in_cluster: list[str] = None):
        if request_ids_in_cluster is None:
            request_ids_in_cluster = []
            
        self.request_id = request_id
        self.same_cluster_restaurant_user_count = same_cluster_restaurant_user_count
        self.request_ids_in_cluster = request_ids_in_cluster

    def to_dict(self) -> dict:
        return {
            'request_id': self.request_id,
            'same_cluster_restaurant_user_count': self.same_cluster_restaurant_user_count,
            'request_ids_in_cluster': self.request_ids_in_cluster
        }

    def __str__(self) -> str:
        return str(self.to_dict())

    def __repr__(self) -> str:
        return str(self.to_dict())

    def __json__(self) -> dict:
        return self.to_dict()

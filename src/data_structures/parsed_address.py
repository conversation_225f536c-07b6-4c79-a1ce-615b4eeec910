import numpy as np


class ParsedAddress:
    def __init__(self, address: str, formatted_address: str, freetext_formatted_address: str, state: str, city: str, district: str, freetext_formatted_corpus_embeddings: np.ndarray = None):
        self.address = address
        self.formatted_address = formatted_address
        self.freetext_formatted_address = freetext_formatted_address
        self.state = state
        self.city = city
        self.district = district
        self.freetext_formatted_corpus_embeddings = freetext_formatted_corpus_embeddings

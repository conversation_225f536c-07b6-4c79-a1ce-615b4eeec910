from datetime import datetime
from typing import List, Optional

from src.utils.config_manager import ConfigManager


class Request:
    def __init__(self, request_id: str, event_timestamp: int, shopee_user_id: int, now_user_id: int,
                 total_discount: int, restaurant_id: int, delivery_address: str, order_id: str = None,
                 dish_category_ids: Optional[List[int]] = None, *args, **kwargs):
        self.request_id = request_id
        self.event_timestamp = event_timestamp
        self.date_str = datetime.fromtimestamp(event_timestamp).strftime('%Y-%m-%d')
        self.now_user_id = now_user_id
        self.shopee_user_id = shopee_user_id
        self.total_discount = total_discount
        self.restaurant_id = restaurant_id
        self.delivery_address = delivery_address
        self.order_id = int(order_id) if order_id else None
        self.dish_category_ids = dish_category_ids or []

    def to_dict(self) -> dict:
        return {
            'request_id': self.request_id,
            'order_id': self.order_id,
            'event_timestamp': self.event_timestamp,
            'date_str': self.date_str,
            'now_user_id': self.now_user_id,
            'shopee_user_id': self.shopee_user_id,
            'total_discount': self.total_discount,
            'restaurant_id': self.restaurant_id,
            'delivery_address': self.delivery_address,
            'dish_category_ids': self.dish_category_ids
        }

    def __str__(self) -> str:
        return str(self.to_dict())

    def __repr__(self) -> str:
        return str(self.to_dict())

    def __json__(self) -> dict:
        return self.to_dict()

    def is_dishes_valid(self) -> bool:
        """
        Check if all dish_category_ids in this request are valid according to ConfigManager.

        Returns:
            bool: True if all dish_category_ids are in ConfigManager.dish_category_ids, False otherwise.
                  Returns True if dish_category_ids is empty.
        """
        if not self.dish_category_ids:
            return True

        valid_dish_category_ids_set = ConfigManager.get_instance().dish_category_ids

        return all(dish_id in valid_dish_category_ids_set for dish_id in self.dish_category_ids)

from abc import ABC, abstractmethod


class IObserver(ABC):
    @abstractmethod
    def update(self, data: any):
        pass


class INotifier(ABC):
    @abstractmethod
    def add_observer(self, observer: IObserver) -> None:
        pass

    @abstractmethod
    def remove_observer(self, observer: IObserver) -> None:
        pass

    @abstractmethod
    def notify_observers(self, data: any) -> None:
        pass

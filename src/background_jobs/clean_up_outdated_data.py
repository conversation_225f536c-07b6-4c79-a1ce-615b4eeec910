import os
from datetime import datetime, timedelta
import schedule
import time
import shutil
import threading

from src.common.consts import HISTORICAL_DATA_PATH, HISTORICAL_CLUSTER_DATA_BASE_PATH, S3_UMS_DATA_PREFIX, \
    HISTORICAL_CLUSTER_DATA_FOLDER
from src.processors.address_clusterer import AddressClusterer
from src.facades.s3_facade import ums_s3
from src.utils.file import create_zip_archive, safe_delete_file
from src.utils.logger import logger


class OutdatedDataCleaner:
    def __init__(self, clusterer: AddressClusterer):
        self.clusterer = clusterer

    def _clean_local_cluster_data(self, date: str) -> None:
        """
        Clean up local cluster data for a specific date.
        Args:
            date: Date string in format YYYY-MM-DD
        """
        cluster_date_path = os.path.join(HISTORICAL_CLUSTER_DATA_FOLDER, date)
        if os.path.exists(cluster_date_path):
            shutil.rmtree(cluster_date_path)
            logger.info(f"Cleaned up local cluster data for date {date}")

    def _clean_s3_cluster_data(self, date: str) -> None:
        """
        Clean up S3 cluster data for a specific date.
        Args:
            date: Date string in format YYYY-MM-DD
        """
        try:
            s3_prefix = f"{S3_UMS_DATA_PREFIX}/{date}"
            s3_files = ums_s3.list_objects(s3_prefix)
            for s3_key in s3_files:
                ums_s3.delete_object(s3_key)
            logger.info(f"Cleaned up S3 data for date {date}")
        except Exception as e:
            logger.error(f"Failed to clean up S3 data for date {date}: {e}")

    def _upload_yesterday_data(self):
        """Upload yesterday's clustering data to S3."""
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        yesterday_path = os.path.join(HISTORICAL_CLUSTER_DATA_FOLDER, yesterday)

        # Ensure data is serialized before uploading
        if not os.path.exists(yesterday_path):
            self.clusterer.serialize_by_date(HISTORICAL_CLUSTER_DATA_FOLDER, yesterday)

        if os.path.exists(yesterday_path):
            # Create zip archive
            archive_name = f"cluster_data_{yesterday}"
            zip_path = create_zip_archive(yesterday_path, archive_name)

            # Upload zip file
            s3_key = f"{S3_UMS_DATA_PREFIX}/{yesterday}/{archive_name}.zip"
            ums_s3.upload_file(zip_path, s3_key)
            logger.info(f"Uploaded yesterday's compressed cluster data to UMS S3 for date {yesterday}")

            # Cleanup temp zip file
            safe_delete_file(zip_path)

    def clean_up_data_at_date(self, date: str):
        """
        Clean up outdated data for a specific date from clusterer, local storage, and S3
        Args:
            date: Date string in format YYYY-MM-DD
        """
        # Clean data from clusterer
        self.clusterer.remove_date_data(date)

        # Clean local order data
        date_path = os.path.join(HISTORICAL_DATA_PATH, date)
        if os.path.exists(date_path):
            shutil.rmtree(date_path)

        # Clean local cluster data
        self._clean_local_cluster_data(date)

        # Clean data from S3
        self._clean_s3_cluster_data(date)

        # Upload yesterday's data to S3
        self._upload_yesterday_data()


def run_scheduler(cleaner: OutdatedDataCleaner):
    def cleanup_job():
        target_date = (datetime.now() - timedelta(days=8)).strftime('%Y-%m-%d')
        cleaner.clean_up_data_at_date(target_date)

    schedule.every().day.at("00:00").do(cleanup_job)

    while True:
        schedule.run_pending()
        time.sleep(60)  # Sleep for 1 minute between checks


def run_daily_cleanup_async(clusterer: AddressClusterer):
    cleaner = OutdatedDataCleaner(clusterer)
    scheduler_thread = threading.Thread(target=run_scheduler, args=(cleaner,), daemon=True)
    scheduler_thread.start()

    return scheduler_thread

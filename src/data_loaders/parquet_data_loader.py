import os
import pandas as pd
from src.data_loaders.data_loader import DataLoader


class ParquetDataLoader(DataLoader):
    def __init__(self, input_path: str):
        self.input_path = input_path

    def load_data(self) -> pd.DataFrame:
        address_df = pd.read_parquet(self.input_path)

        address_df['date_str'] = pd.to_datetime(address_df['event_timestamp'], unit='s').dt.strftime('%Y-%m-%d')

        return address_df

    def save_data(self, data: pd.DataFrame, output_path: str) -> None:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        data.to_parquet(output_path)

if __name__ == '__main__':
    data_loader = ParquetDataLoader(input_path='/Users/<USER>/Working/Shopee/address-clustering-model-service/data/order/2025-03-18/part-00000-84b0eeee-24fe-4ca8-be0d-f67557461576.c000.zstd.parquet')
    data = data_loader.load_data()
    data_loader.save_data(data.head(10), '/Users/<USER>/Working/Shopee/address-clustering-model-service/data/order/bak/demo.parquet')

import os

import pandas as pd

from src.data_loaders.data_loader import DataLoader
from src.utils.logger import logger


class CSVDataLoader(DataLoader):
    def __init__(self, input_path: str):
        self.input_path = input_path

    def load_data(self) -> pd.DataFrame:
        return pd.read_csv(self.input_path, na_filter=False)

    def save_data(self, data: pd.DataFrame, output_path: str) -> None:
        logger.info(f"Saving {len(data)} rows to {output_path}")

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        data.to_csv(output_path, index=False)
